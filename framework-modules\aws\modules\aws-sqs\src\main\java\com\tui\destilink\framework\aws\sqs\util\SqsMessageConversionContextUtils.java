package com.tui.destilink.framework.aws.sqs.util;

import com.tui.destilink.framework.aws.sqs.components.message_source.ExtendedQueueAttributes;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import io.awspring.cloud.sqs.support.converter.SqsMessageConversionContext;
import lombok.experimental.UtilityClass;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.Optional;

@UtilityClass
public class SqsMessageConversionContextUtils {

    public static boolean isFifoQueue(SqsMessageConversionContext context) {
        return extractExtendedQueueAttributes(context).isFifo();
    }

    public static ExtendedQueueAttributes extractExtendedQueueAttributes(SqsMessageConversionContext context) {
        Assert.isInstanceOf(ExtendedQueueAttributes.class, context.getQueueAttributes(), "ExtendedQueueAttributes not found");
        return (ExtendedQueueAttributes) Objects.requireNonNull(validate(context).getQueueAttributes());
    }

    public static Optional<ExtendedQueueAttributes.RedrivePolicy> extractRedrivePolicy(SqsMessageConversionContext context) {
        return Optional.ofNullable(extractExtendedQueueAttributes(context).getRedrivePolicy());
    }

    public static boolean hasRedrivePolicy(SqsMessageConversionContext context) {
        return extractRedrivePolicy(context).isPresent();
    }

    public static String extractQueueName(SqsMessageConversionContext context) {
        return Objects.requireNonNull(validate(context).getQueueAttributes()).getQueueName();
    }

    public static String extractQueueUrl(SqsMessageConversionContext context) {
        return Objects.requireNonNull(validate(context).getQueueAttributes()).getQueueUrl();
    }

    @SuppressWarnings("unchecked")
    public static AcknowledgementCallback<Object> extractAcknowledgementCallback(SqsMessageConversionContext context) {
        return (AcknowledgementCallback<Object>) validate(context).getAcknowledgementCallback();
    }

    private static SqsMessageConversionContext validate(SqsMessageConversionContext context) {
        Assert.notNull(context, "context must not be null");
        return context;
    }
}
