package com.tui.destilink.framework.caching.cachemanager;

import lombok.Data;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
public class CompositeCacheManager implements CacheManager {

    private final CacheManager defaultCacheManager;

    private final Map<String, CacheManager> cacheManagers;

    private final Map<String, Cache> caches;

    public CompositeCacheManager(Map<String, CacheManager> cacheManagers) {
        this(null, cacheManagers);
    }

    public CompositeCacheManager(CacheManager defaultCacheManager, Map<String, CacheManager> cacheManagers) {
        this.defaultCacheManager = defaultCacheManager;
        this.cacheManagers = cacheManagers;
        this.caches = cacheManagers.values().stream().flatMap(CompositeCacheManager::getCachesFromCacheManager)
                .collect(Collectors.toMap(Cache::getName, Function.identity()));
    }

    @Override
    public Cache getCache(String name) {
        Cache cache = caches.getOrDefault(name, null);
        if (cache == null && defaultCacheManager != null) {
            // Try to create the missing cache
            synchronized (defaultCacheManager) {
                // Another thread could have created the new cache while the current
                // one was waiting for the synchronized block
                cache = caches.getOrDefault(name, null);
                if (cache == null) {
                    cache = defaultCacheManager.getCache(name);
                    if (cache != null) {
                        caches.put(name, cache);
                    }
                }
            }
        }
        return cache;
    }

    @Override
    public Collection<String> getCacheNames() {
        return caches.keySet();
    }

    public CacheManager getCacheManagerForKeyspacePrefix(String keyspacePrefix) {
        return cacheManagers.get(keyspacePrefix);
    }

    private static Stream<Cache> getCachesFromCacheManager(CacheManager cacheManager) {
        return cacheManager.getCacheNames().stream()
                .map(cacheManager::getCache);
    }

}
