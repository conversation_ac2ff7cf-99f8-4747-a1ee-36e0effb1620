package com.tui.destilink.framework.aws.sns;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties;
import com.tui.destilink.framework.aws.sns.resolver.TopicTagsResolver;
import com.tui.destilink.framework.aws.sns.resolver.impl.CachingTopicTagsResolver;
import com.tui.destilink.framework.aws.sns.send.SnsSender;
import com.tui.destilink.framework.aws.sns.send.fifo.DefaultFifoAttributesEnricher;
import com.tui.destilink.framework.aws.sns.send.fifo.FifoAttributesEnricher;
import com.tui.destilink.framework.aws.sns.util.SnsUtils;
import io.awspring.cloud.sns.core.CachingTopicArnResolver;
import io.awspring.cloud.sns.core.SnsTemplate;
import io.awspring.cloud.sns.core.TopicArnResolver;
import io.awspring.cloud.sns.core.TopicsListingTopicArnResolver;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import software.amazon.awssdk.services.sns.SnsClient;

@AutoConfiguration
@EnableConfigurationProperties({DefaultSnsContainerProperties.class})
@ConditionalOnClass({SnsClient.class, SnsTemplate.class})
@AutoConfigureAfter({io.awspring.cloud.autoconfigure.sns.SnsAutoConfiguration.class})
@ConditionalOnProperty(name = "spring.cloud.aws.sns.enabled", havingValue = "true", matchIfMissing = true)
public class SnsAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    TopicArnResolver topicArnResolver(SnsClient snsClient) {
        return new CachingTopicArnResolver(new TopicsListingTopicArnResolver(snsClient));
    }

    @Bean
    @ConditionalOnMissingBean
    TopicTagsResolver topicTagsResolver(SnsClient snsClient, TopicArnResolver topicArnResolver) {
        return new CachingTopicTagsResolver(snsClient, topicArnResolver);
    }

    @Bean
    @ConditionalOnMissingBean
    FifoAttributesEnricher fifoAttributesEnricher() {
        return new DefaultFifoAttributesEnricher();
    }

    @Bean
    @ConditionalOnMissingBean
    SnsSender snsSender(SnsClient snsClient, ObjectMapper objectMapper, TopicArnResolver topicArnResolver, DefaultSnsContainerProperties properties, FifoAttributesEnricher fifoAttributesEnricher) {
        return SnsUtils.getSnsSender(snsClient, objectMapper, topicArnResolver, properties, fifoAttributesEnricher);
    }

}