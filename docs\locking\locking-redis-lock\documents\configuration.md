# Redis Locking Module: Configuration

## 1. Introduction

This document details the configuration property hierarchy and key settings for the `locking-redis-lock` module. A clear understanding of this hierarchy is essential for correctly configuring lock behavior globally, for a logical bucket (defined programmatically), and for individual lock instances.

The guiding principles for this configuration are:
*   **Clear Override Precedence**: Instance-specific settings (via builders) > Programmatic Bucket Configuration (via builders) > Global Configuration (YAML/Java defaults).
*   **Restricted Overrides**: Core operational behaviors are fixed at appropriate levels to ensure stability.
*   **Clarity**: Property names and descriptions are designed to be unambiguous.
*   **Safety Levels**: Properties are categorized by developer override safety.
*   **Mandatory Unlock Notifications**: Redis Pub/Sub for unlock notifications is a core, non-optional mechanism, with one listener per bucket.

## 2. Configuration Hierarchy and Flow

The configuration follows a simplified hierarchy. Global defaults are loaded from YAML into `RedisLockProperties`. The `LockBucketRegistry` uses these global defaults to initialize a `LockBucketConfig` when a new bucket context is created via the builder. Specific properties can then be overridden programmatically through the builder chain for that bucket or for individual lock instances.

```mermaid
graph TD
    subgraph YAML_Configuration ["YAML Configuration (`1000-locking-redis-lock.application.yml`)"]
        direction LR
        A_GlobalYAML["Global Settings<br>(destilink.fw.locking.redis.*)"]
    end

    subgraph Java_Configuration_Classes ["Java Configuration Classes"]
        direction LR
        C_RedisLockProps["`RedisLockProperties.java`<br>(@ConfigurationProperties)<br>Holds Global Defaults"]
        E_LockBucketConfig["`LockBucketConfig.java`<br>(Resolved effective settings for a specific bucket,<br>initialized from Global, then builder overrides.)"]
    end

    subgraph Builder_Chain ["Builder Chain (Bucket & Instance Configuration)"]
        direction LR
        F_LockBucketRegistry["`LockBucketRegistry` (Bean - Lock Factory)"]
        G_LockBucketBuilder["`LockBucketBuilder`<br>(Defines Bucket Name, Scope, Owner Supplier.<br>Sets Bucket-level defaults for some properties like `useWatchdog`)"]
        H_LockConfigBuilder["`LockConfigBuilder`<br>(Lock Type Selection)"]
        I_AbstractLockTypeConfigBuilder["`AbstractLockTypeConfigBuilder`<br>(Instance Overrides for chosen lock type)"]
        J_SpecificLockBuilders["(e.g., `ReentrantLockConfigBuilder`, `StateLockConfigBuilder`)<br>(Type-specific params, Further Instance Overrides)"]
    end

    subgraph Lock_Instance ["Lock Instance"]
        K_LockInstance["Actual Lock Instance (e.g., RedisReentrantLock)<br>Operates with resolved configuration"]
    end

    A_GlobalYAML -- "populates" --> C_RedisLockProps;
    
    F_LockBucketRegistry -- "uses global defaults from" --> C_RedisLockProps;
    F_LockBucketRegistry -- "initializes with global defaults" --> E_LockBucketConfig;
    
    E_LockBucketConfig -- "Provides initial defaults to" --> G_LockBucketBuilder;
    G_LockBucketBuilder -- "Can override some bucket-level defaults in" --> E_LockBucketConfig;
    G_LockBucketBuilder -- "Passes config & transitions to" --> H_LockConfigBuilder;
    H_LockConfigBuilder -- "Passes config & transitions to" --> I_AbstractLockTypeConfigBuilder;
    I_AbstractLockTypeConfigBuilder -- "Is extended by/passes config to" --> J_SpecificLockBuilders;
    J_SpecificLockBuilders -- ".build() creates" --> K_LockInstance;

    I_AbstractLockTypeConfigBuilder -- "Applies *allowed* instance overrides over" --> E_LockBucketConfig;
    K_LockInstance -- "Receives final effective configuration from" --> J_SpecificLockBuilders;

    style A_GlobalYAML fill:#f9f,stroke:#333,stroke-width:2px
    style C_RedisLockProps fill:#ccf,stroke:#333,stroke-width:2px
    style E_LockBucketConfig fill:#fdd,stroke:#333,stroke-width:2px
    style F_LockBucketRegistry fill:#cff,stroke:#333,stroke-width:2px
    style G_LockBucketBuilder fill:#cff,stroke:#333,stroke-width:2px
    style H_LockConfigBuilder fill:#cff,stroke:#333,stroke-width:2px
    style I_AbstractLockTypeConfigBuilder fill:#cff,stroke:#333,stroke-width:2px
    style J_SpecificLockBuilders fill:#cff,stroke:#333,stroke-width:2px
    style K_LockInstance fill:#cfc,stroke:#333,stroke-width:2px
```

**Flow Explanation:**

1.  **YAML Loading**: Spring Boot loads global properties from `1000-locking-redis-lock.application.yml` (under `destilink.fw.locking.redis.*`) into the `RedisLockProperties` bean.
2.  **Bucket Configuration Initialization**: When `LockBucketRegistry.builder("bucketName", [applicationInstanceId])` is called:
    *   A `LockBucketConfig` object is created.
    *   It's initialized with values from the global `RedisLockProperties`.
3.  **Programmatic Bucket-Level Overrides**: The `LockBucketBuilder` allows setting certain bucket-wide policies (like `useWatchdog`, scope, `lockOwnerSupplier`). These settings modify the `LockBucketConfig` for all locks derived from this builder instance.
4.  **Builder Chain**: The (potentially modified) `LockBucketConfig` is passed through the subsequent builder chain (`LockConfigBuilder` -> `AbstractLockTypeConfigBuilder` -> specific lock builder).
5.  **Instance Overrides**: Allowed properties (like `leaseTime`, `retryInterval`) can be further overridden at the instance level via methods on `AbstractLockTypeConfigBuilder` or specific lock builders.
6.  **Lock Instantiation**: The `build()` method on the final specific lock builder creates the lock instance (e.g., `RedisReentrantLock`) with the final, effective configuration.

## 3. Key Configuration Properties

The following table details the primary configuration properties, their scope, and override capabilities, aligned with `lock-final/redis_lock_configuration_proposal.md`.

| Property (YAML Key / Java Field)                                  | Description                                                                                                                                                                                             | Default (Global YAML)        | Default (Java `RedisLockProperties`) | `LockBucketConfig` Default Source | Configuration Level          | Programmatic Bucket Override (Builder) | Programmatic Instance Override (Builder)                                                                                                                                                                                             | Effective Value Logic (Proposed)                                                                                                                                                                                                                           | Developer Override Safety | Notes                                                                                                                                                                                                                                                        |
| :---------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------- | :------------------------------------------ | :-------------------------------- | :--------------------------- | :------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Core Operational Settings**                                     |                                                                                                                                                                                                       |                              |                                             |                                   |                              |                                        |                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                          |                           |                                                                                                                                                                                                                                                |
| `use-watchdog` / `useWatchdog`                                    | Policy: whether the watchdog *can be used* for locks associated with this configuration. Actual use per instance is conditional.                                                                         | `true`                       | `true`                                      | Global                            | Global, Programmatic Bucket  | `LockBucketBuilder.useWatchdog(boolean)` | **N/A (Fixed at Bucket Level)**                                                                                                                                                                                                  | `LockBucketBuilder.useWatchdog` (if set) > Global `RedisLockProperties.useWatchdog`.                                                                                                                                                              | Fixed at Bucket           | If false, watchdog is never used. If true, it's conditionally used per instance (app-bound & `leaseTime` > `watchdogMaxTtl`).                                                                                                          |
| `lease-time` / `leaseTime`                                        | The total duration a lock should be held. If `useWatchdog` is active for the instance, the watchdog ensures renewal up to this time. If not, this is the direct TTL set on the Redis key.                    | `PT60S`                      | `Duration.ofSeconds(60)`                    | Global                            | Global, Programmatic Bucket, Instance | `LockBucketBuilder.withDefaultLeaseTime(Duration)` | [`AbstractLockTypeConfigBuilder.withLeaseTime(Duration)`](framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/builder/AbstractLockTypeConfigBuilder.java:0) | Instance Builder `leaseTime` (if set) > Bucket Builder `defaultLeaseTime` (if set) > Global `RedisLockProperties.leaseTime`.                                                                                                                            | Instance Overrideable (Safe) | Developer-defined desired lock holding time.                                                                                                                                                                                             |
| `retry-interval` / `retryInterval`                                | Interval between lock acquisition attempts if the initial attempt fails and Pub/Sub notification hasn't arrived. Used as a short poll after waiting for Pub/Sub.                                      | `PT0.1S`                     | `Duration.ofMillis(100)`                    | Global                            | Global, Programmatic Bucket, Instance | `LockBucketBuilder.withDefaultRetryInterval(Duration)` | [`AbstractLockTypeConfigBuilder.withRetryInterval(Duration)`](framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/builder/AbstractLockTypeConfigBuilder.java:94) | Instance Builder `retryInterval` (if set) > Bucket Builder `defaultRetryInterval` (if set) > Global `RedisLockProperties.retryInterval`.                                                                                                               | Instance Overrideable (Safe) | Controls fallback polling frequency.                                                                                                                                                                                                   |
| **Watchdog Specific Settings (Internal Global)**                  | These settings control the behavior of the `LockWatchdog` service.                                                                                                                                      |                              |                                             |                                   |                              |                                        |                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                          |                           |                                                                                                                                                                                                                                                |
| `watchdog-max-ttl` / `watchdogMaxTtl`                             | **Internal Global Property.** The maximum TTL value the watchdog sets on a Redis key at each renewal interval.                                                                                          | `PT60S`                      | `Duration.ofSeconds(60)`                    | N/A                               | Global                       | N/A                                    | **N/A (Not Overrideable)**                                                                                                                                                                                                   | Global `RedisLockProperties.watchdogMaxTtl`.                                                                                                                                                                                                  | Internal                  | If a lock's `leaseTime` is <= `watchdogMaxTtl`, watchdog is not used.                                                                                                                                                                |
| `watchdog-renewal-multiplier` / `watchdogRenewalMultiplier`       | **Internal Global Property.** Determines renewal frequency: Renewal Interval = `watchdogMaxTtl / watchdogRenewalMultiplier`.                                                                              | `3`                          | `3`                                         | N/A                               | Global                       | N/A                                    | **N/A (Not Overrideable)**                                                                                                                                                                                                   | Global `RedisLockProperties.watchdogRenewalMultiplier`.                                                                                                                                                                                             | Internal                  | Example: `watchdogMaxTtl`=60s, `multiplier`=3 -> renewal attempt every 20s.                                                                                                                                                    |
| **Other Internal Settings**                                       | These are primarily for internal operation and should not be changed by developers.                                                                                                                     |                              |                                             |                                   |                              |                                        |                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                          |                           |                                                                                                                                                                                                                                                |
| `uuid-cache-ttl-seconds` / `uuidCacheTtlSeconds`                  | TTL for idempotency response cache entries (seconds).                                                                                                                                                   | `300`                        | `300`                                       | N/A                               | Global                       | N/A                                    | **N/A (Not Overrideable)**                                                                                                                                                                                                   | Global `RedisLockProperties.uuidCacheTtlSeconds`.                                                                                                                                                                                                   | Internal                  | Internal caching.                                                                                                                                                                                                          |
| `health-indicator.enabled` (YAML)                                 | Enable/disable custom health indicator for locking.                                                                                                                                                     | `true`                       | N/A (Auto-configured)                       | N/A                               | Global                       | N/A                                    | **N/A (Not Overrideable)**                                                                                                                                                                                                   | Global YAML `health-indicator.enabled`.                                                                                                                                                                                                      | Internal                  |                                                                                                                                                                                                                                                |
| **StateLock Specific**                                            |                                                                                                                                                                                                       |                              |                                             |                                   |                              |                                        |                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                          |                           |                                                                                                                                                                                                                                                |
| `state-key-expiration` / `stateTtl`                               | TTL for state keys used by `RedisStateLock`.                                                                                                                                                             | `P1D`                        | `Duration.ofDays(1)`                        | Global                            | Global, Programmatic Bucket, Instance | `LockBucketBuilder.withDefaultStateKeyExpiration(Duration)` | [`StateLockConfigBuilder.withStateExpiration(Duration)`](framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/builder/StateLockConfigBuilder.java:85) | Instance Builder `stateExpiration` (if set) > Bucket Builder `defaultStateKeyExpiration` (if set) > Global `RedisLockProperties.stateKeyExpiration`.                                                                                                       | Instance Overrideable (Safe) | Specific to `StateLock`.                                                                                                                                                                                                                   |

**Notes on Property Interaction:**
*   The `retryInterval` is the primary mechanism for determining wait time between lock attempts after an initial Pub/Sub wait (which is implicitly tied to the current lock holder's TTL or a very short internal timeout if the lock is free but an unlock message is processed).
*   The `UnlockMessageListenerManager` creates one `UnlockMessageListener` per bucket. There is no longer a subscription strategy or specific notification timeout configurable via properties for the listener itself; its behavior is fixed.

## 4. Java Configuration Classes

*   **[`RedisLockProperties.java`](../src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockProperties.java:1)**:
    *   Annotated with `@ConfigurationProperties(prefix = "destilink.fw.locking.redis")`.
    *   Holds global default values for properties like `leaseTime`, `retryInterval`, `watchdogMaxTtl`, `watchdogRenewalMultiplier`, `uuidCacheTtlSeconds`, `stateKeyExpiration`, and `healthIndicator.enabled`.
    *   **No longer contains a `Map<String, BucketConfig> buckets` for YAML-based bucket definitions.**
*   **[`LockBucketConfig.java`](../src/main/java/com/tui/destilink/framework/locking/redis/config/LockBucketConfig.java:1)**:
    *   A non-Spring managed class instantiated by `LockBucketRegistry` and configured by `LockBucketBuilder`.
    *   Represents the *resolved* configuration for a specific bucket, initialized from global `RedisLockProperties` and then potentially overridden by `LockBucketBuilder` methods for defaults (`defaultLeaseTime`, `defaultRetryInterval`, `useWatchdog`, `defaultStateKeyExpiration`).
    *   Provides the effective default settings that subsequent builders use as their base.

This configuration structure provides flexibility while ensuring that critical operational parameters are controlled at appropriate levels, promoting stability and safe usage of the locking module.