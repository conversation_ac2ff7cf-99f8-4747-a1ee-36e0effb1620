package com.tui.destilink.framework.aws.sns.utils;

import com.tui.destilink.framework.aws.sns.exception.SnsFrameworkException;
import com.tui.destilink.framework.aws.sns.util.MessageAttributeUtils;
import com.tui.destilink.framework.trips.core.logging.TripsContextConstants;
import io.awspring.cloud.sns.core.MessageAttributeDataTypes;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;

class MessageAttributeUtilsTest {

    @Test
    void givenTooManyHeadersShouldThrowException() {
        //given
        Map<String, String> header = new HashMap<>();

        for (int i = 0; i <= 10; i++) {
            header.put("key" + i, "value" + i);
        }

        //when
        try {
            MessageAttributeUtils.toMessageAttributeMap(header, false);
            fail("Exception expected");
        } catch (SnsFrameworkException e) {
            //then
            assertThat(e.getMessage()).isEqualTo("Number of message attributes [11] exceeds the allowed maximum [10].");
        }
    }

    @Test
    void shouldConvertToMessageAttributeMap() {
        //given
        Map<String, String> map = Map.of("header1", "value1");

        //when
        MDC.put(TripsContextConstants.BUSINESS_PROCESS_ID, "test123");
        Map<String, MessageAttributeValue> messageAttributeMap = MessageAttributeUtils.toMessageAttributeMap(map, true);

        //then
        assertThat(messageAttributeMap.entrySet()).hasSize(2);
        final MessageAttributeValue attribute1 = messageAttributeMap.get("header1");
        assertThat(attribute1.stringValue()).isEqualTo("value1");
        assertThat(attribute1.dataType()).isEqualTo(MessageAttributeDataTypes.STRING);

        final MessageAttributeValue attributeBpid = messageAttributeMap.get("businessProcessId");
        assertThat(attributeBpid.stringValue()).isEqualTo("test123");
        assertThat(attributeBpid.dataType()).isEqualTo(MessageAttributeDataTypes.STRING);
    }

    @Test
    void shouldConvertToHeaderMap() {
        //given
        Map<String, MessageAttributeValue> messageAttributeMap = new HashMap<>();
        messageAttributeMap.put("header1", MessageAttributeUtils.getAttributeForString("value1"));

        //when
        Map<String, String> headerMap = MessageAttributeUtils.toHeaderMap(messageAttributeMap);

        //then
        assertThat(headerMap.entrySet()).hasSize(1);
        assertThat(headerMap).containsEntry("header1", "value1");
    }

    @Test
    void shouldBeSorted() {
        //given
        Map<String, MessageAttributeValue> messageAttributeMap = new HashMap<>();
        messageAttributeMap.put("header1", MessageAttributeUtils.getAttributeForString("value1"));
        messageAttributeMap.put("x-header2", MessageAttributeUtils.getAttributeForString("value2"));
        messageAttributeMap.put("header3", MessageAttributeUtils.getAttributeForString("value3"));

        //when
        Map<String, String> headerMap = MessageAttributeUtils.toHeaderMap(messageAttributeMap);

        //then
        assertThat(headerMap.entrySet()).hasSize(3);
        assertThat(headerMap.keySet()).containsExactly("header1", "header3", "x-header2");
    }
}