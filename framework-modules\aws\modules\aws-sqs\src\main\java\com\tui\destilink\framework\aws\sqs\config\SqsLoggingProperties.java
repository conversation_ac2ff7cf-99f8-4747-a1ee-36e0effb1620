package com.tui.destilink.framework.aws.sqs.config;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import static com.tui.destilink.framework.aws.sqs.config.SqsLoggingProperties.PREFIX;

@Data
@ConfigurationProperties(prefix = PREFIX, ignoreUnknownFields = false)
@Validated
public class SqsLoggingProperties {

    public static final String PREFIX = "destilink.fw.aws.sqs.logging";

    @NotNull
    private Boolean enabled = true;
}
