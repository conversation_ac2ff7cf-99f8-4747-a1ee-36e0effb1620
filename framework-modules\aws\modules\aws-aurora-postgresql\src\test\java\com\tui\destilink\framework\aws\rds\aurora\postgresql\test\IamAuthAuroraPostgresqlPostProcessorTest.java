package com.tui.destilink.framework.aws.rds.aurora.postgresql.test;

import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles({"iam-auth"})
class IamAuthAuroraPostgresqlPostProcessorTest extends AbstractAuroraPostgresqlPostProcessorTest {

    @Override
    protected String testGetSomeUserUsername() {
        return "iam-" + super.testGetSomeUserUsername();
    }

    @Override
    protected String test1WrapperPluginsPrefix() {
        return "iam,";
    }
}
