package com.tui.destilink.framework.aws.sns.config;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Data
@ConfigurationProperties(prefix = "destilink.fw.aws.sns.container.default", ignoreUnknownFields = false)
@Validated
public class DefaultSnsContainerProperties {

    @NotNull
    private SnsLogging snsLogging;

    private Boolean enrichTripsContext;

    @Data
    public static class SnsLogging {
        @NotNull
        private Boolean enabled;
        @NotNull
        private Boolean prettyPrint;
    }
}