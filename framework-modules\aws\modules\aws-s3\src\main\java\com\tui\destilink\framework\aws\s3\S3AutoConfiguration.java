package com.tui.destilink.framework.aws.s3;

import com.tui.destilink.framework.aws.s3.crt.S3AssumingRoleCrtClientFactory;
import lombok.NoArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import software.amazon.awssdk.crt.s3.S3Client;
import software.amazon.awssdk.services.s3.S3AsyncClient;

@AutoConfiguration
@ConditionalOnProperty(name = "spring.cloud.aws.s3.enabled", havingValue = "true", matchIfMissing = true)
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class S3AutoConfiguration {

    @Configuration
    @ConditionalOnClass({S3Client.class, S3AsyncClient.class})
    @Import(S3AssumingRoleCrtClientFactory.class)
    static class S3CrtClientAutoConfiguration {
    }
}
