package com.tui.destilink.framework.aws.sqs.listener.retry;

import com.tui.destilink.framework.aws.sqs.util.MessagingMessageHeadersUtils;
import org.springframework.core.MethodParameter;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.invocation.HandlerMethodArgumentResolver;
import org.springframework.util.ClassUtils;

public class SqsListenerRetryableHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return ClassUtils.isAssignable(SqsListenerRetryable.class, parameter.getParameterType());
    }

    @Override
    public SqsListenerRetryable resolveArgument(MethodParameter parameter, Message<?> message) {
        SqsListenerRetryableHandler delegate = MessagingMessageHeadersUtils.extractSqsListenerRetryable(message);
        return new SqsListenerRetryable() {

            @Override
            public void allowRetry() {
                delegate.allowRetry();
            }

            @Override
            public void notAllowRetry() {
                delegate.notAllowRetry();
            }
        };
    }
}
