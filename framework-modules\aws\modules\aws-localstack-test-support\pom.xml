<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.aws</groupId>
        <artifactId>aws</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>aws-localstack-test-support</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-sns</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-sqs</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-s3</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>opensearch</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-opensearch</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>gitlab-ci-unit-tests-framework-modules-deactivate</id>
            <activation>
                <property>
                    <name>env.CI_UNIT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>aws</value>
                </property>
            </activation>
            <properties>
                <skipTests>true</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-int-tests-framework-modules-deactivate</id>
            <activation>
                <property>
                    <name>env.CI_INT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>aws</value>
                </property>
            </activation>
            <properties>
                <skipTests>true</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-unit-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_UNIT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>aws-localstack-test-support</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>false</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-int-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_INT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>aws-localstack-test-support</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>false</skip.integrationTests>
            </properties>
        </profile>
    </profiles>

</project>
