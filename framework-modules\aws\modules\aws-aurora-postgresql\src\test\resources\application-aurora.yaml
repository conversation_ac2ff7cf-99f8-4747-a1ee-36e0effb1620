spring:
  datasource:
    url: **********************************************************************************************************************************************
    username: destilink-framework-ci-tests
    password:
    driver-class-name: software.amazon.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
  flyway:
    baseline-on-migrate: true
destilink:
  fw:
    aws:
      rds:
        aurora:
          postgresql:
            hikari:
              iam-auth:
                enabled: true
logging:
  level:
    org.postgresql: debug
    software.amazon.jdbc: debug
debug: false