package com.tui.destilink.framework.aws.test.localstack;

import com.tui.destilink.framework.aws.test.localstack.annotations.*;
import com.tui.destilink.framework.aws.test.localstack.services.AbstractService;
import com.tui.destilink.framework.aws.test.localstack.services.LocalStackMessagingService;
import com.tui.destilink.framework.aws.test.localstack.services.LocalStackOpenSearchService;
import com.tui.destilink.framework.aws.test.localstack.services.LocalStackS3Service;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import com.tui.destilink.framework.core.util.BeanUtils;
import com.tui.destilink.framework.test.support.core.util.TestSupportPropertiesUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.DefaultSingletonBeanRegistry;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextCustomizer;
import org.springframework.test.context.MergedContextConfiguration;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.LOCALSTACK;

@Slf4j
public class LocalStackContextCustomizer implements ContextCustomizer {

    public static final String PROPERTY_KEY_SUFFIX_RESOURCE_PREFIX = "resource-prefix";

    private final String resourcePrefix;

    private final LocalStack localStack;

    private final Set<S3Bucket> s3Buckets;

    private final Set<SqsQueue> sqsQueues;

    private final Set<SnsTopic> snsTopics;

    private final Set<SnsSubscription> snsSubscriptions;

    private final Set<OpenSearchDomain> openSearchDomains;

    public LocalStackContextCustomizer(String resourcePrefix, LocalStack localStack, Set<S3Bucket> s3Buckets, Set<SqsQueue> sqsQueues, Set<SnsTopic> snsTopics, Set<SnsSubscription> snsSubscriptions) {
        this.resourcePrefix = resourcePrefix;
        this.localStack = localStack;
        this.s3Buckets = mergeS3Buckets(localStack, s3Buckets);
        this.sqsQueues = mergeSqsQueues(localStack, sqsQueues);
        this.snsTopics = mergeSnsTopics(localStack, snsTopics);
        this.snsSubscriptions = mergeSnsSubscriptions(localStack, snsSubscriptions);
        this.openSearchDomains = mergeOpenSearchDomains(localStack);
    }

    @Override
    public void customizeContext(ConfigurableApplicationContext context, MergedContextConfiguration mergedConfig) {
        if (resourcePrefix != null) {
            TestSupportPropertiesUtils.forPropertyKey(context, LOCALSTACK).addProperty(resourcePrefix, PROPERTY_KEY_SUFFIX_RESOURCE_PREFIX);
        }
        ConfigurableListableBeanFactory beanFactory = context.getBeanFactory();
        Assert.isInstanceOf(DefaultSingletonBeanRegistry.class, beanFactory);
        setupS3Bean(context, beanFactory);
        setupMessagingBean(context, beanFactory);
        setupOpenSearchBean(context, beanFactory);
        BeanUtils.registerBean(beanFactory, LocalStackPropertiesResolver.class.getName(), new LocalStackPropertiesResolver(context));
    }

    private void setupOpenSearchBean(ConfigurableApplicationContext context, ConfigurableListableBeanFactory beanFactory) {
        if (openSearchDomains.isEmpty()) {
            return;
        }
        LocalStackOpenSearchService openSearchService = new LocalStackOpenSearchService(localStack,
                openSearchDomains, context, resourcePrefix);
        registerBean(beanFactory, "localStackOpenSearchService", openSearchService);
    }

    private void setupS3Bean(ConfigurableApplicationContext context, ConfigurableListableBeanFactory beanFactory) {
        boolean cloudEvent = snsTopics.stream().anyMatch(SnsTopic::isCloudEvent);
        if (s3Buckets.isEmpty() && !cloudEvent) {
            return;
        }
        LocalStackS3Service s3Service = new LocalStackS3Service(localStack, s3Buckets, cloudEvent, context, resourcePrefix);
        registerBean(beanFactory, "localStackS3Service", s3Service);
    }

    private void setupMessagingBean(ConfigurableApplicationContext context, ConfigurableListableBeanFactory beanFactory) {
        if (sqsQueues.isEmpty() && snsTopics.isEmpty() && snsSubscriptions.isEmpty()) {
            return;
        }
        LocalStackMessagingService messagingService = new LocalStackMessagingService(localStack, sqsQueues, snsTopics, snsSubscriptions, context, resourcePrefix);
        registerBean(beanFactory, "localStackMessagingService", messagingService);
    }

    private Set<S3Bucket> mergeS3Buckets(LocalStack localStack, Set<S3Bucket> s3Buckets) {
        final Set<S3Bucket> target = new HashSet<>(s3Buckets);
        target.addAll(Arrays.asList(localStack.s3Buckets()));
        return target;
    }

    private Set<SqsQueue> mergeSqsQueues(LocalStack localStack, Set<SqsQueue> sqsQueues) {
        final Set<SqsQueue> target = new HashSet<>(sqsQueues);
        target.addAll(Arrays.asList(localStack.sqsQueues()));
        return target;
    }

    private Set<SnsTopic> mergeSnsTopics(LocalStack localStack, Set<SnsTopic> snsTopics) {
        final Set<SnsTopic> target = new HashSet<>(snsTopics);
        target.addAll(Arrays.asList(localStack.snsTopics()));
        return target;
    }

    private Set<SnsSubscription> mergeSnsSubscriptions(LocalStack localStack, Set<SnsSubscription> snsSubscriptions) {
        final Set<SnsSubscription> target = new HashSet<>(snsSubscriptions);
        target.addAll(Arrays.asList(localStack.snsSubscriptions()));
        return target;
    }

    private Set<OpenSearchDomain> mergeOpenSearchDomains(LocalStack localStack) {
        final Set<OpenSearchDomain> target = new HashSet<>();
        if (StringUtils.hasLength(localStack.openSearchDomain().name())) {
            target.add(localStack.openSearchDomain());
        }
        return target;
    }

    private <T extends DisposableBean> void registerBean(ConfigurableListableBeanFactory beanFactory, String beanName, T bean) {
        try {
            beanFactory.registerSingleton(beanName, bean);
            beanFactory.initializeBean(bean, beanName);
            ((DefaultSingletonBeanRegistry) beanFactory).registerDisposableBean(beanName, bean);
        } catch (Exception ex) {
            log.error("Failed to initialize bean {}", beanName, ex);
            destroyAllRegistered(beanFactory);
            throw new IllegalStateException("Failed to initialize bean " + beanName, ex);
        }
    }

    private void destroyAllRegistered(ConfigurableListableBeanFactory beanFactory) {
        Map<String, ? extends AbstractService> beans = beanFactory.getBeansOfType(AbstractService.class);
        for (Map.Entry<String, ? extends AbstractService> entry : beans.entrySet()) {
            try {
                entry.getValue().destroy();
            } catch (Exception ex) {
                log.error("Failed to destroy bean {}", entry.getKey(), ex);
            }
        }
    }

}
