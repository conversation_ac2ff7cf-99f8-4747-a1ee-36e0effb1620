package com.tui.destilink.framework.aws.test.localstack.test;

import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.regions.providers.AwsRegionProvider;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles({"it"})
class RegionIT {

    @Autowired
    private AwsRegionProvider awsRegionProvider;

    @Test
    @DirtiesContext
    void testRegion() {
        assertThat(awsRegionProvider).isNotNull();
        assertThat(awsRegionProvider.getRegion()).isEqualTo(Region.EU_CENTRAL_1);
    }
}
