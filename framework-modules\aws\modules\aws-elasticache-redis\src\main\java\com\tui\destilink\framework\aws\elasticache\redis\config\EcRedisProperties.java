package com.tui.destilink.framework.aws.elasticache.redis.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import static com.tui.destilink.framework.aws.elasticache.redis.config.EcRedisProperties.PROPERTIES_PREFIX;

@Data
@Validated
@ConfigurationProperties(prefix = PROPERTIES_PREFIX, ignoreUnknownFields = false)
public class EcRedisProperties {

    public static final String PROPERTIES_PREFIX = "destilink.fw.aws.elasticache.redis";

    @NotNull
    private String host;
    @NotNull
    private Integer port = 6379;
    @Valid
    @NotNull
    private EcRedisProperties.IamAuth iamAuth = new IamAuth();

    @Data
    public static class IamAuth {
        @NotNull
        @NotBlank
        private String userId;
        @NotNull
        @NotBlank
        private String replicationGroupId;
        @NotNull
        @Range(min = 60, max = 900)
        private Integer tokenExpirationSeconds = 900;
    }
}
