package com.tui.destilink.framework.caching.condition;


import com.tui.destilink.framework.caching.config.CachingProperties;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Conditional(OnCacheBackendCondition.class)
public @interface ConditionalOnCacheBackend {

    CachingProperties.BackendBindingType value();

}
