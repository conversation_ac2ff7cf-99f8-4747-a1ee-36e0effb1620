package com.tui.destilink.framework.aws.opensearch;

import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHttpEntityEnclosingRequest;
import org.apache.http.message.BasicHttpRequest;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpCoreContext;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.signer.Aws4Signer;
import software.amazon.awssdk.auth.signer.params.Aws4SignerParams;
import software.amazon.awssdk.regions.Region;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

class AWSRequestSigningInterceptorTest {

    private static AWSRequestSigningInterceptor createInterceptor() {
        final AwsBasicCredentials awsCredentials = AwsBasicCredentials.create("keyId", "accessKey");
        return new AWSRequestSigningInterceptor(Aws4Signer.create() /* NOSONAR */, () -> Aws4SignerParams.builder()
                .signingName("service")
                .signingRegion(Region.EU_CENTRAL_1)
                .awsCredentials(awsCredentials).build());
    }

    @Test
    void testSimpleSigner() throws Exception {
        HttpEntityEnclosingRequest request =
                new BasicHttpEntityEnclosingRequest("GET", "/query?a=b");
        request.setEntity(new StringEntity("I'm an entity"));
        request.addHeader("foo", "bar");
        request.addHeader("content-length", "0");

        HttpCoreContext context = new HttpCoreContext();
        context.setTargetHost(HttpHost.create("localhost"));

        createInterceptor().process(request, context);

        assertEquals("bar", request.getFirstHeader("foo").getValue());
        assertNotNull(request.getFirstHeader("Authorization").getValue());
        assertNull(request.getFirstHeader("content-length"));
    }

    @Test
    void testBadRequest() throws Exception {
        try {
            HttpRequest badRequest = new BasicHttpRequest("GET", "?#!@*%");
            createInterceptor().process(badRequest, new BasicHttpContext());
            fail();
        } catch (IOException e) {
            Assertions.assertThat(e.getMessage()).isEqualTo("Invalid URI");
        }
    }

    @Test
    void testEncodedUriSigner() throws Exception {
        final String uri = "/foo-2017-02-25%2Cfoo-2017-02-26/_search?a=b";
        HttpEntityEnclosingRequest request = new BasicHttpEntityEnclosingRequest("GET", uri);
        request.setEntity(new StringEntity("I'm an entity"));
        request.addHeader("foo", "bar");
        request.addHeader("content-length", "0");

        HttpCoreContext context = new HttpCoreContext();
        context.setTargetHost(HttpHost.create("localhost"));

        createInterceptor().process(request, context);

        assertEquals("bar", request.getFirstHeader("foo").getValue());
        assertNotNull(request.getFirstHeader("Authorization").getValue());
        assertNull(request.getFirstHeader("content-length"));
        assertEquals(uri, request.getRequestLine().getUri());
    }

}