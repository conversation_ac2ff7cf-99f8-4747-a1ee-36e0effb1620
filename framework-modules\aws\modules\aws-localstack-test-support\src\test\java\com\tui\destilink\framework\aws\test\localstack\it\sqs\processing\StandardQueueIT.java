package com.tui.destilink.framework.aws.test.localstack.it.sqs.processing;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchResultEntry;

import java.time.Duration;
import java.util.List;

import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.SqsConsumers.*;
import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.TestMessageConverter.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class StandardQueueIT extends AbstractSqsProcessingIT {

    @Test
    void testQueue(CapturedOutput output) {
        List<SendMessageBatchRequestEntry> entries = buildListenerTestMessages(null);
        List<SendMessageBatchResultEntry> results = sendMessages(QUEUE_NAME, entries);
        wait(QUEUE_NAME);

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            results.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
        });

        assertThat(getQueueMessageCount(QUEUE_NAME)).isZero();
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(entries.size()).hasSize(results.size());
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NONE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)).hasSize(2);
    }

    @Test
    void testQueueDlq(CapturedOutput output) {
        List<SendMessageBatchRequestEntry> entries = buildListenerTestMessages(null);
        List<SendMessageBatchResultEntry> results = sendMessages(QUEUE_WITH_DLQ_NAME, entries);
        wait(QUEUE_WITH_DLQ_NAME);

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            results.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isEqualTo(2);
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "DLQ forward will be handled by SQS")).isEqualTo(2);
        });

        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_NAME)).isEqualTo(4);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(entries.size()).hasSize(results.size());
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NONE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE)).hasSize(3);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)).hasSize(3);
    }

    @Test
    void testQueueDlqSingleRetry(CapturedOutput output) {
        List<SendMessageBatchRequestEntry> entries = buildListenerTestMessages(null);
        List<SendMessageBatchResultEntry> results = sendMessages(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME, entries);
        wait(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME);

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            results.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isZero();
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "DLQ forward will be handled by SQS")).isEqualTo(2);
        });

        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME)).isEqualTo(4);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(entries.size()).hasSize(results.size());
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NONE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)).hasSize(1);
    }

    @Test
    void testConverterQueue(CapturedOutput output) {
        List<SendMessageBatchRequestEntry> entries = buildConverterTestMessages(null);
        List<SendMessageBatchResultEntry> results = sendMessages(QUEUE_NAME, entries);
        wait(QUEUE_NAME);
        assertThat(getQueueMessageCount(QUEUE_NAME)).isZero();
        assertThat(TestMessageConverter.CONVERTED_MESSAGES).hasSize(entries.size()).hasSize(results.size());
        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Received SQS message id=")).isEqualTo(2);
        });
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_SUCCESS)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_NULL_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_ERROR_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(2);
    }

    @Test
    void testConverterQueueDlq(CapturedOutput output) {
        List<SendMessageBatchRequestEntry> entries = buildConverterTestMessages(null);
        List<SendMessageBatchResultEntry> results = sendMessages(QUEUE_WITH_DLQ_NAME, entries);
        wait(QUEUE_WITH_DLQ_NAME);
        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_NAME)).isEqualTo(5);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES).hasSize(entries.size()).hasSize(results.size());
        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isEqualTo(5);
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Received SQS message id=")).isEqualTo(3);
        });
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_SUCCESS)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_NULL_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_ERROR_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE)).hasSize(3);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)).hasSize(3);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(3);
    }

    @Test
    void testConverterQueueDlqSingleRetry(CapturedOutput output) {
        List<SendMessageBatchRequestEntry> entries = buildConverterTestMessages(null);
        List<SendMessageBatchResultEntry> results = sendMessages(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME, entries);
        wait(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME);
        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_SINGLE_RETRY_NAME)).isEqualTo(7);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES).hasSize(entries.size()).hasSize(results.size());
        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isEqualTo(7);
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Received SQS message id=")).isEqualTo(1);
        });
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_SUCCESS)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_NULL_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_ERROR_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND)).hasSize(1);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(1);
    }
}
