package com.tui.destilink.framework.aws.test.localstack.it.sqs.processing;

import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.SqsQueue;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchResponse;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchResultEntry;

import java.time.Duration;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.AbstractSqsProcessingIT.*;
import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.SqsConsumers.*;
import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.TestMessageConverter.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@SpringBootTest(classes = {TestApplication.class, TestConfig.class, SqsConsumers.class},
        webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles({"it"/*, "kubernetes"*/})
@LocalStack(
        sqsQueues = {
                @SqsQueue(name = QUEUE_NAME),
                @SqsQueue(name = QUEUE_WITH_DLQ_NAME, createDlq = true, maxReceiveCount = 3),
                @SqsQueue(name = QUEUE_WITH_DLQ_SINGLE_RETRY_NAME, createDlq = true, maxReceiveCount = 1),
                @SqsQueue(name = QUEUE_FIFO_NAME),
                @SqsQueue(name = QUEUE_WITH_DLQ_FIFO_NAME, createDlq = true, maxReceiveCountFifo = 6),
                @SqsQueue(name = QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, createDlq = true, maxReceiveCountFifo = 2),
        }
)
@ExtendWith(OutputCaptureExtension.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
abstract class AbstractSqsProcessingIT {

    public static final String QUEUE_NAME = "queue";
    public static final String QUEUE_WITH_DLQ_NAME = "queue-with-dlq";
    public static final String QUEUE_WITH_DLQ_SINGLE_RETRY_NAME = "queue-with-dlq-single-retry";
    public static final String QUEUE_FIFO_NAME = "queue.fifo";
    public static final String QUEUE_WITH_DLQ_FIFO_NAME = "queue-with-dlq.fifo";
    public static final String QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME = "queue-with-dlq-single-retry.fifo";

    @Autowired
    protected SqsAsyncClient sqsClient;

    @Autowired
    protected LocalStackPropertiesResolver propResolver;

    @BeforeEach
    protected void setup() {
        TestMessageConverter.CONVERTED_MESSAGES.clear();
        RECEIVED_MESSAGES.clear();
        RECEIVED_MESSAGES_ORDERED.clear();
    }

    protected List<SendMessageBatchResultEntry> sendMessages(String queueName, List<SendMessageBatchRequestEntry> entries) {
        List<SendMessageBatchRequestEntry> batch = new LinkedList<>();
        List<SendMessageBatchResultEntry> result = new LinkedList<>();
        for (SendMessageBatchRequestEntry entry : entries) {
            batch.add(entry);
            if (batch.size() >= 10) {
                result.addAll(sendBatch(queueName, batch));
                batch.clear();
            }
        }
        if (!batch.isEmpty()) {
            result.addAll(sendBatch(queueName, batch));
        }
        assertThat(result).hasSize(entries.size());
        return result;
    }

    private List<SendMessageBatchResultEntry> sendBatch(String queueName, List<SendMessageBatchRequestEntry> batch) {
        SendMessageBatchResponse response = sqsClient
                .sendMessageBatch(b -> b.queueUrl(propResolver.getQueueUrl(queueName)).entries(batch))
                .join();
        assertThat(response.hasFailed()).isFalse();
        return response.successful();
    }

    protected List<SendMessageBatchRequestEntry> buildListenerTestMessages(String messageGroupId) {
        return Stream.of(
                        SQS_LISTENER_MESSAGE_NONE,
                        SQS_LISTENER_MESSAGE_ACK,
                        SQS_LISTENER_MESSAGE_ACK_ASYNC,
                        SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN,
                        SQS_LISTENER_MESSAGE_ACK_RETRYABLE,
                        SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE,
                        SQS_LISTENER_MESSAGE_EXCEPTION,
                        SQS_LISTENER_MESSAGE_EXCEPTION_ACK,
                        SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE,
                        SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)
                .map(m -> SendMessageBatchRequestEntry.builder()
                        .id(UUID.randomUUID().toString())
                        .messageBody(m)
                        .messageAttributes(buildConverterHeaderMap(SQS_CONVERTER_HEADER_SUCCESS)))
                .map(b -> {
                    if (messageGroupId != null) {
                        b.messageDeduplicationId(UUID.randomUUID().toString())
                                .messageGroupId(messageGroupId);
                    }
                    return b.build();
                })
                .toList();
    }

    protected List<SendMessageBatchRequestEntry> buildConverterTestMessages(String messageGroupId) {
        return Stream.of(
                        SQS_CONVERTER_HEADER_SUCCESS,
                        SQS_CONVERTER_HEADER_NULL_MESSAGE,
                        SQS_CONVERTER_HEADER_EXCEPTION,
                        SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND,
                        SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD,
                        SQS_CONVERTER_HEADER_ERROR_MESSAGE,
                        SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE,
                        SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND,
                        SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)
                .map(h -> SendMessageBatchRequestEntry.builder()
                        .id(UUID.randomUUID().toString())
                        .messageBody(SQS_LISTENER_MESSAGE_ACK)
                        .messageAttributes(buildConverterHeaderMap(h)))
                .map(b -> {
                    if (messageGroupId != null) {
                        b.messageDeduplicationId(UUID.randomUUID().toString())
                                .messageGroupId(messageGroupId);
                    }
                    return b.build();
                })
                .toList();
    }

    protected void wait(String queueName) {
        wait(queueName, Duration.ofSeconds(60));
    }

    protected void wait(String queueName, Duration timeout) {
        await()
                .timeout(timeout)
                .pollDelay(Duration.ofSeconds(10))
                .pollInterval(Duration.ofSeconds(2))
                .until(() -> getQueueMessageCount(queueName) <= 0);
    }

    protected int getDlqCount(String queueName) {
        return getQueueMessageCountInternal(propResolver.getQueueDlqUrl(queueName));
    }

    protected int getQueueMessageCount(String queueName) {
        return getQueueMessageCountInternal(propResolver.getQueueUrl(queueName));
    }

    private int getQueueMessageCountInternal(String queueUrl) {
        return sqsClient.getQueueAttributes(b -> b.queueUrl(queueUrl).attributeNames(QueueAttributeName.ALL))
                .thenApply(r -> Integer.parseInt(r.attributes().get(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES))
                        + Integer.parseInt(r.attributes().get(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES_DELAYED))
                        + Integer.parseInt(r.attributes().get(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES_NOT_VISIBLE)))
                .join();
    }
}
