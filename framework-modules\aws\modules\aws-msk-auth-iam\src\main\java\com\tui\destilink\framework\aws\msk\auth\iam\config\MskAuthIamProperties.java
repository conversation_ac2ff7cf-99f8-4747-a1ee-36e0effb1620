package com.tui.destilink.framework.aws.msk.auth.iam.config;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import static com.tui.destilink.framework.aws.msk.auth.iam.config.MskAuthIamProperties.PREFIX;

@Data
@Validated
@ConfigurationProperties(prefix = PREFIX)
public class MskAuthIamProperties {

    public static final String PREFIX = "destilink.fw.aws.msk.auth.iam";

    @NotNull
    private Boolean enabled = true;

    @NotNull
    private Boolean setAwsStsRegion = true;

    @NotNull
    private Boolean debugCreds = false;

    @NotNull
    @Pattern(regexp = "arn:aws:iam::(\\d{12})?:role\\/[\\w+=,.@-]{1,64}")
    private String roleArn;

}
