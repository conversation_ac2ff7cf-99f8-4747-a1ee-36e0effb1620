package com.tui.destilink.framework.aws.sns.send;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.SnsAutoConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;

import java.sql.Date;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@EnableAutoConfiguration(exclude = {io.awspring.cloud.autoconfigure.sns.SnsAutoConfiguration.class, SnsAutoConfiguration.class})
class SnsPayloadSerializerTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void givenLocalDateShouldSerializeAsExpected() throws JsonProcessingException {
        LocalDate payload = LocalDate.of(2022, 2, 2);
        assertThat(objectMapper.writeValueAsString(payload)).isEqualTo("\"2022-02-02\"");
    }

    @Test
    void givenDateShouldSerializeAsExpected() throws JsonProcessingException {
        Date payload = Date.valueOf(LocalDate.of(2022, 2, 2));
        assertThat(objectMapper.writeValueAsString(payload)).isEqualTo("\"2022-02-02\"");
    }

    @Test
    void givenOffsetDateTimeShouldSerializeAsExpected() throws JsonProcessingException {
        OffsetDateTime payload = OffsetDateTime.of(2022, 2, 2, 1, 3, 4, 5, ZoneOffset.UTC);
        assertThat(objectMapper.writeValueAsString(payload)).isEqualTo("\"2022-02-02T01:03:04.000000005Z\"");
    }
}