package com.tui.destilink.framework.caching.cachemanager;

import lombok.Data;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cache.CacheManager;

import java.util.Map;

@Data
public class CacheManagersProvider<T extends CacheManager> implements InitializingBean, DisposableBean {

    private final Map<String, T> cacheManagers;

    private boolean initialized = false;

    @Override
    public void afterPropertiesSet() {
        if (initialized) {
            return;
        }
        cacheManagers.entrySet().stream()
                .filter(e -> e.getValue() instanceof InitializingBean)
                .forEach(e -> {
                    try {
                        ((InitializingBean) e.getValue()).afterPropertiesSet();
                    } catch (Exception ex) {
                        throw new BeanInitializationException("Failed to initialize cache manager " + e.getKey(), ex);
                    }
                });
        initialized = true;
    }

    @Override
    public void destroy() {
        if (!initialized) {
            return;
        }
        cacheManagers.entrySet().stream()
                .filter(e -> e.getValue() instanceof DisposableBean)
                .forEach(e -> {
                    try {
                        ((DisposableBean) e.getValue()).destroy();
                    } catch (Exception ex) {
                        throw new IllegalStateException("Failed to initialize cache manager " + e.getKey(), ex);
                    }
                });
        initialized = false;
    }


}
