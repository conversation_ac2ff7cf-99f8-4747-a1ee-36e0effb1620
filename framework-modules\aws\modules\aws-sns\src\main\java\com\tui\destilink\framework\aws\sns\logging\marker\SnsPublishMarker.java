package com.tui.destilink.framework.aws.sns.logging.marker;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SnsPublishMarker {

    private static final String TRUNCATED_SUFFIX = "...(truncated)";
    private static final int HEADER_MAX_LENGTH = 500 - TRUNCATED_SUFFIX.length();
    private static final String FIELD_NAME = "aws.sns.publish";

    public static LogstashMarker of(PublishRequest request, String topicArn) {
        return of(request, null, topicArn, false);
    }

    public static LogstashMarker of(PublishRequest request, String topicArn, boolean skipMessageBody) {
        return of(request, null, topicArn, skipMessageBody);
    }

    public static LogstashMarker of(PublishRequest request, PublishResponse response, String topicArn) {
        return of(request, response, topicArn, false);
    }

    public static LogstashMarker of(PublishRequest request, PublishResponse response, String topicArn, boolean skipMessageBody) {
        return Markers.append(FIELD_NAME, SnsPublishData.of(request, response, topicArn, skipMessageBody));
    }

    @Data
    @Builder
    public static class SnsPublishData {
        private final String topicArn;
        private final String messageId;
        private final String sequenceNumber;
        private final String messageGroupId;
        private final String messageDeduplicationId;
        private final String message;
        private final Map<String, String> messageAttributes;

        public static SnsPublishData of(PublishRequest request, PublishResponse response, String topicArn, boolean skipMessageBody) {
            SnsPublishData.SnsPublishDataBuilder builder = builder();
            if (request != null) {
                builder
                        .messageGroupId(request.messageGroupId())
                        .messageDeduplicationId(request.messageDeduplicationId())
                        .message(skipMessageBody ? "[REDACTED]" : request.message())
                        .messageAttributes(convertMessageAttributes(request.messageAttributes()));
            }
            if (response != null) {
                builder
                        .messageId(response.messageId())
                        .sequenceNumber(response.sequenceNumber());
            }
            if (topicArn != null) {
                builder.topicArn(topicArn);
            }
            return builder.build();
        }

        private static Map<String, String> convertMessageAttributes(Map<String, MessageAttributeValue> messageAttributes) {
            Map<String, String> result = new HashMap<>();
            if (messageAttributes != null) {
                messageAttributes.forEach((k, v) -> result.put(k, truncate(v.toString())));
            }
            return result;
        }

        private static String truncate(String str) {
            if (str.length() > HEADER_MAX_LENGTH) {
                return str.substring(0, HEADER_MAX_LENGTH) + TRUNCATED_SUFFIX;
            }
            return str;
        }
    }
}
