package com.tui.destilink.framework.aws.sqs.listener.invoke;

import com.tui.destilink.framework.aws.sqs.components.message_source.AcknowledgementCallbackWrapper;
import com.tui.destilink.framework.aws.sqs.components.message_source.ExtendedQueueAttributes;
import com.tui.destilink.framework.aws.sqs.listener.retry.SqsListenerRetryableHandler;
import com.tui.destilink.framework.aws.sqs.tracing.execution_interceptor.SqsMessageTraceUtils;
import com.tui.destilink.framework.aws.sqs.util.DeadLetterQueueUtils;
import com.tui.destilink.framework.aws.sqs.util.MessagingMessageHeadersUtils;
import com.tui.destilink.framework.core.logging.context.util.MessagingMessageContextSnapshotUtils;
import com.tui.destilink.framework.core.logging.filter.mute.LogMutingException;
import com.tui.destilink.framework.core.logging.monitoring.MonitoringMarkerUtils;
import com.tui.destilink.framework.core.logging.monitoring.MonitoringMoodData;
import com.tui.destilink.framework.core.messaging.message_handler.aspect.InterceptingMessageHandlerAroundInvoke;
import com.tui.destilink.framework.core.tracing.TracingUtils;
import com.tui.destilink.framework.core.util.FutureUtils;
import io.awspring.cloud.sqs.SqsAcknowledgementException;
import io.awspring.cloud.sqs.listener.SqsHeaders;
import io.micrometer.context.ContextSnapshot;
import io.opentracing.Scope;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.util.Assert;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName;

import java.util.Optional;

@Slf4j
public class TracingMessageHandlerInvokeInterceptor extends InterceptingMessageHandlerAroundInvoke {

    private final SqsAsyncClient sqsAsyncClient;

    @Autowired
    public TracingMessageHandlerInvokeInterceptor(SqsAsyncClient sqsAsyncClient) {
        this.sqsAsyncClient = sqsAsyncClient;
    }

    @Override
    public Object doInvoke(ProceedingJoinPoint pjp, Message<?> message) throws Throwable {
        try (Scope ignoreParentScope = SqsMessageTraceUtils.activateRootSpan(message);
             ContextSnapshot.Scope ignoreSnapshotScope = MessagingMessageContextSnapshotUtils.activate(message)) {
            // Setup
            String queueUrl = null;
            software.amazon.awssdk.services.sqs.model.Message sourceMessage = null;
            AcknowledgementCallbackWrapper<?> ackCallback = null;
            SqsListenerRetryableHandler retryable;
            ExtendedQueueAttributes queueAttributes = null;
            try {
                queueUrl = message.getHeaders().get(SqsHeaders.SQS_QUEUE_URL_HEADER, String.class);
                Assert.notNull(queueUrl, "queueUrl must not be null");
                sourceMessage = message.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class);
                Assert.notNull(sourceMessage, "sourceMessage must not be null");
                ackCallback = MessagingMessageHeadersUtils.extractAcknowledgementCallbackWrapper(message);
                Assert.notNull(ackCallback, "ackCallback must not be null");
                retryable = MessagingMessageHeadersUtils.extractSqsListenerRetryable(message);
                Assert.notNull(retryable, "retryable must not be null");
                queueAttributes = MessagingMessageHeadersUtils.extractExtendedQueueAttributes(message);
                Assert.notNull(queueAttributes, "queueAttributes must not be null");
            } catch (Exception ex) {
                TracingUtils.setErrorOnSpan(ex, log, true);
                log.error(MonitoringMarkerUtils.appendForMonitoringMood(MonitoringMoodData.Mood.BOMB), "An unexpected error occurred while preparing the SqsListener to process message id={} from queue={}", message.getHeaders().getId(), queueUrl, ex);
                handleSetupException(message, queueUrl, ackCallback, queueAttributes, sourceMessage);
                return null;
            }
            // Process message
            try {
                return handleProcessingResult(pjp.proceed(), message, queueUrl, ackCallback, retryable, queueAttributes, sourceMessage);
            } catch (LogMutingException ex) {
                throw ex;
            } catch (Exception ex) {
                TracingUtils.setErrorOnSpan(ex, log, true);
                log.error("An unexpected error occurred while processing the message id={} from queue={}", message.getHeaders().getId(), queueUrl, ex);
                handleProcessingException(message, queueUrl, ackCallback, retryable, queueAttributes, sourceMessage);
                return null;
            }
        } finally {
            SqsMessageTraceUtils.finish(message);
            MDC.clear();
        }
    }

    private void handleSetupException(Message<?> message, String queueUrl, AcknowledgementCallbackWrapper<?> ackCallback, ExtendedQueueAttributes queueAttributes, software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        DeadLetterQueueUtils.forwardMessage(sqsAsyncClient, message);
        boolean ackResult = handleAckAndGetResult(message, queueUrl, ackCallback);
        if (!ackResult && queueAttributes.isFifo()) {
            logFifoOrderNoLongerGuaranteed(sourceMessage);
        }
    }

    private Object handleProcessingResult(Object result, Message<?> message, String queueUrl, AcknowledgementCallbackWrapper<?> ackCallback, SqsListenerRetryableHandler sqsListenerRetryableHandler, ExtendedQueueAttributes queueAttributes, software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        if (ackCallback.getAckResult() != null) {
            // Message ack in listener
            boolean ackResult = handleAckAndGetResult(message, queueUrl, ackCallback);
            if (ackResult) {
                return result;
            }
            // Ack failed
            handleFailedAck(message, sourceMessage, queueAttributes, sqsListenerRetryableHandler);
            return null;
        }
        // Message not ack in listener
        boolean allowRetry = sqsListenerRetryableHandler.getAllowRetry().get();
        if (allowRetry) {
            return handleProcessingResultAllowRetry(message, queueUrl, ackCallback, sqsListenerRetryableHandler, queueAttributes, sourceMessage);
        } else {
            return handleProcessingResultNotAllowRetry(message, queueUrl, ackCallback, sqsListenerRetryableHandler, queueAttributes, sourceMessage);
        }
    }

    private Object handleProcessingResultAllowRetry(Message<?> message, String queueUrl, AcknowledgementCallbackWrapper<?> ackCallback, SqsListenerRetryableHandler sqsListenerRetryableHandler, ExtendedQueueAttributes queueAttributes, software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        // Retry is allowed in listener
        if (!sqsListenerRetryableHandler.isRetryPossible()) {
            if (sqsListenerRetryableHandler.isManualHandlingRequired()) {
                // Manually forward to DLQ and delete
                // Else let SQS handle the DLQ and delete process
                log.error("Message id={} from queue={} is marked as retryable but retry is not possible", message.getHeaders().getId(), queueUrl);
                DeadLetterQueueUtils.forwardMessage(sqsAsyncClient, message);
                if (!handleAckAndGetResult(message, queueUrl, ackCallback)) {
                    handleFailedAck(message, sourceMessage, queueAttributes, sqsListenerRetryableHandler);
                }
            } else {
                log.error("Message id={} from queue={} is marked as retryable but retry is not possible - DLQ forward will be handled by SQS", message.getHeaders().getId(), queueUrl);
            }
            return null;
        } else {
            log.error("Retrying message id={} from queue={} not acknowledged in listener", message.getHeaders().getId(), queueUrl);
            throw new LogMutingException();
        }
    }

    private Object handleProcessingResultNotAllowRetry(Message<?> message, String queueUrl, AcknowledgementCallbackWrapper<?> ackCallback, SqsListenerRetryableHandler sqsListenerRetryableHandler, ExtendedQueueAttributes queueAttributes, software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        // Retry is not allowed in listener
        log.error("Message id={} from queue={} not acknowledged in listener and retry is not possible", message.getHeaders().getId(), queueUrl);
        if (sqsListenerRetryableHandler.isRetryPossible() || sqsListenerRetryableHandler.isManualHandlingRequired()) {
            // Manually forward to DLQ and delete
            // Else let SQS handle the DLQ and delete process
            DeadLetterQueueUtils.forwardMessage(sqsAsyncClient, message);
            if (!handleAckAndGetResult(message, queueUrl, ackCallback)) {
                handleFailedAck(message, sourceMessage, queueAttributes, sqsListenerRetryableHandler);
            }
        }
        return null;
    }

    private void handleProcessingException(Message<?> message, String queueUrl, AcknowledgementCallbackWrapper<?> ackCallback, SqsListenerRetryableHandler sqsListenerRetryableHandler, ExtendedQueueAttributes queueAttributes, software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        if (ackCallback.getAckResult() != null) {
            log.warn("Message id={} from queue={} was already acknowledged in listener", message.getHeaders().getId(), queueUrl);
            // Message ack in listener
            boolean ackResult = handleAckAndGetResult(message, queueUrl, ackCallback);
            if (ackResult) {
                return;
            }
            // Ack failed
            handleFailedAck(message, sourceMessage, queueAttributes, sqsListenerRetryableHandler);
            return;
        }
        handleProcessingResult(null, message, queueUrl, ackCallback, sqsListenerRetryableHandler, queueAttributes, sourceMessage);
    }

    private boolean handleAckAndGetResult(Message<?> message, String queueUrl, AcknowledgementCallbackWrapper<?> ackCallback) {
        try {
            if (ackCallback == null) {
                log.error(MonitoringMarkerUtils.appendForMonitoringMood(MonitoringMoodData.Mood.BOMB), "No AcknowledgementCallback found for message id={} from queue={} failed", message.getHeaders().getId(), queueUrl);
                return false;
            }
            ackCallback.onAcknowledgeCast(message);
            ackCallback.getAckResult().join();
            return true;
        } catch (Exception ex) {
            TracingUtils.setErrorOnSpan(FutureUtils.unwrapCompletionException(ex), log, true);
            Throwable cause = FutureUtils.unwrapCompletionException(ex);
            if (!(cause instanceof SqsAcknowledgementException)) {
                log.error("Acknowledgement of message id={} from queue={} failed - Message might get processed again", message.getHeaders().getId(), queueUrl, cause);
            }
            return false;
        }
    }

    private void handleFailedAck(Message<?> message, software.amazon.awssdk.services.sqs.model.Message sourceMessage, ExtendedQueueAttributes queueAttributes, SqsListenerRetryableHandler sqsListenerRetryableHandler) throws LogMutingException {
        if (queueAttributes.isFifo()) {
            if (sqsListenerRetryableHandler.isRetryPossible()) {
                log.info("Skipping all pending messages of group={} until the visibility timeout expires", extractMessageGroupId(sourceMessage));
                throw new LogMutingException();
            } else {
                DeadLetterQueueUtils.forwardMessage(sqsAsyncClient, message);
                logFifoOrderNoLongerGuaranteed(message);
            }
        } else {
            throw new LogMutingException();
        }
    }

    private void logFifoOrderNoLongerGuaranteed(Message<?> message) {
        software.amazon.awssdk.services.sqs.model.Message sqsMessage = Optional.ofNullable(message)
                .map(m -> m.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class))
                .orElse(null);
        logFifoOrderNoLongerGuaranteed(sqsMessage);
    }

    private void logFifoOrderNoLongerGuaranteed(software.amazon.awssdk.services.sqs.model.Message message) {
        log.error(MonitoringMarkerUtils.appendForMonitoringMood(MonitoringMoodData.Mood.BOMB), "The order of FIFO message group={} can no longer be guaranteed", extractMessageGroupId(message));
    }

    private String extractMessageGroupId(software.amazon.awssdk.services.sqs.model.Message message) {
        return Optional.ofNullable(message)
                .map(software.amazon.awssdk.services.sqs.model.Message::attributes)
                .map(attributes -> attributes.get(MessageSystemAttributeName.MESSAGE_GROUP_ID))
                .orElse(null);
    }
}
