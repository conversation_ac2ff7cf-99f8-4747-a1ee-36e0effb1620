<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>framework-modules</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>com.tui.destilink.framework.aws</groupId>
    <artifactId>aws</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>modules/aws-core</module>
        <module>modules/aws-sns</module>
        <module>modules/aws-sqs</module>
        <module>modules/aws-s3</module>
        <module>modules/aws-msk-auth-iam</module>
        <module>modules/aws-elasticache-redis</module>
        <module>modules/aws-opensearch</module>
        <module>modules/aws-localstack-test-support</module>
        <module>modules/aws-aurora-postgresql</module>
    </modules>

    <profiles>
        <profile>
            <id>gitlab-ci-unit-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_UNIT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>aws</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>false</skip.unitTests>
                <skip.integrationTests>true</skip.integrationTests>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-int-tests-framework-modules</id>
            <activation>
                <property>
                    <name>env.CI_INT_TEST_JOB_FRAMEWORK_MODULES</name>
                    <value>aws</value>
                </property>
            </activation>
            <properties>
                <skipTests>false</skipTests>
                <skip.unitTests>true</skip.unitTests>
                <skip.integrationTests>false</skip.integrationTests>
            </properties>
        </profile>
    </profiles>

</project>
