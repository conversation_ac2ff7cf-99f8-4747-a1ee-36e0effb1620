package com.tui.destilink.framework.aws.elasticache.redis.auth;

import io.lettuce.core.RedisCredentials;
import io.lettuce.core.RedisCredentialsProvider;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;

@RequiredArgsConstructor
public class RedisIamAuthCredentialsProvider implements RedisCredentialsProvider {

    private final String userId;
    private final long tokenExpirationSeconds;
    private final AwsCredentialsProvider awsCredentialsProvider;
    private final IamAuthTokenRequest iamAuthTokenRequest;

    @Override
    public Mono<RedisCredentials> resolveCredentials() {
        return Mono.just(IamRedisCredentials.of(userId, awsCredentialsProvider, iamAuthTokenRequest, calculateTokenExpirationSeconds()));
    }

    private long calculateTokenExpirationSeconds() {
        return tokenExpirationSeconds <= 120 ? tokenExpirationSeconds - 30 : tokenExpirationSeconds - 60;
    }
}
