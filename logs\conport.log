2025-05-28 11:38:39,761 - INFO - __main__ - Logging to file: ./logs/conport.log
2025-05-28 11:38:39,761 - INFO - __main__ - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: ${workspaceFolder}
2025-05-28 11:38:39,761 - WARNING - __main__ - MAIN.PY: WARNING - Workspace ID was literally '${workspaceFolder}'. This variable was not expanded by the client IDE. Falling back to current working directory as workspace_id: c:\Users\<USER>\Desktop\destilink-framework. Ensure CWD in MCP config ('c:\Users\<USER>\Desktop\destilink-framework') is the correct project workspace.
2025-05-28 11:38:39,761 - INFO - __main__ - STDIO mode: Using effective_workspace_id 'c:\Users\<USER>\Desktop\destilink-framework'. Database directory will be created on first actual DB use.
2025-05-28 11:38:39,777 - INFO - __main__ - ConPort FastMCP server lifespan starting.
2025-05-28 11:38:39,781 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-28 11:38:39,783 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourcesRequest
2025-05-28 11:38:39,784 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourceTemplatesRequest
2025-05-28 11:39:13,571 - INFO - __main__ - Logging to file: ./logs/conport.log
2025-05-28 11:39:13,571 - INFO - __main__ - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: C:\Users\<USER>\Desktop\destilink-framework
2025-05-28 11:39:13,571 - INFO - __main__ - STDIO mode: Using effective_workspace_id 'C:\Users\<USER>\Desktop\destilink-framework'. Database directory will be created on first actual DB use.
2025-05-28 11:39:13,575 - INFO - __main__ - ConPort FastMCP server lifespan starting.
2025-05-28 11:39:13,577 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-28 11:39:13,580 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourcesRequest
2025-05-28 11:39:13,581 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourceTemplatesRequest
2025-05-29 00:17:05,242 - INFO - __main__ - Logging to file: ./logs/conport.log
2025-05-29 00:17:05,243 - INFO - __main__ - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: C:\Users\<USER>\Desktop\destilink-framework
2025-05-29 00:17:05,243 - INFO - __main__ - STDIO mode: Using effective_workspace_id 'C:\Users\<USER>\Desktop\destilink-framework'. Database directory will be created on first actual DB use.
2025-05-29 00:17:05,247 - INFO - __main__ - ConPort FastMCP server lifespan starting.
2025-05-29 00:17:06,359 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-29 00:17:06,382 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourcesRequest
2025-05-29 00:17:06,387 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourceTemplatesRequest
2025-05-29 00:41:18,160 - INFO - __main__ - Logging to file: ./logs/conport.log
2025-05-29 00:41:18,160 - INFO - __main__ - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: C:\Users\<USER>\Desktop\destilink-framework
2025-05-29 00:41:18,160 - INFO - __main__ - STDIO mode: Using effective_workspace_id 'C:\Users\<USER>\Desktop\destilink-framework'. Database directory will be created on first actual DB use.
2025-05-29 00:41:18,170 - INFO - __main__ - ConPort FastMCP server lifespan starting.
2025-05-29 00:41:18,176 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-29 00:41:18,181 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourcesRequest
2025-05-29 00:41:18,183 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourceTemplatesRequest
2025-05-29 00:51:50,427 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-29 00:51:50,544 - INFO - mcp.server.lowlevel.server - Processing request of type CallToolRequest
2025-05-29 00:57:44,909 - INFO - mcp.server.lowlevel.server - Processing request of type CallToolRequest
2025-05-29 00:58:18,111 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-29 00:58:18,234 - INFO - mcp.server.lowlevel.server - Processing request of type CallToolRequest
2025-05-29 03:40:54,344 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-29 03:40:54,612 - INFO - mcp.server.lowlevel.server - Processing request of type CallToolRequest
2025-05-29 03:40:54,618 - INFO - context_portal_mcp.core.embedding_service - Loading Sentence Transformer model: all-MiniLM-L6-v2...
2025-05-29 03:40:54,620 - INFO - sentence_transformers.SentenceTransformer - Use pytorch device_name: cpu
2025-05-29 03:40:54,620 - INFO - sentence_transformers.SentenceTransformer - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-29 03:40:57,278 - INFO - context_portal_mcp.core.embedding_service - Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully.
2025-05-29 03:40:57,360 - INFO - context_portal_mcp.db.vector_store_service - Vector store path set to: c:\Users\<USER>\Desktop\destilink-framework\context_portal\conport_vector_data
2025-05-29 03:40:57,360 - INFO - context_portal_mcp.db.vector_store_service - Initializing ChromaDB client for workspace 'c:\Users\<USER>\Desktop\destilink-framework' at path: c:\Users\<USER>\Desktop\destilink-framework\context_portal\conport_vector_data
2025-05-29 03:40:57,562 - INFO - context_portal_mcp.db.vector_store_service - Getting or creating ChromaDB collection 'conport_semantic_store' for workspace 'c:\Users\<USER>\Desktop\destilink-framework'.
2025-05-29 03:40:57,563 - INFO - context_portal_mcp.core.embedding_service - Creating Chroma SentenceTransformerEmbeddingFunction for model: all-MiniLM-L6-v2
2025-05-29 03:40:57,563 - INFO - sentence_transformers.SentenceTransformer - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-05-29 03:40:59,072 - INFO - context_portal_mcp.db.vector_store_service - Successfully upserted embedding for doc_id 'progress_entry_4'.
2025-05-29 03:40:59,072 - INFO - context_portal_mcp.handlers.mcp_handlers - Successfully generated and stored embedding for progress entry ID 4
2025-05-29 13:57:37,325 - INFO - __main__ - Logging to file: ./logs/conport.log
2025-05-29 13:57:37,325 - INFO - __main__ - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: C:\Users\<USER>\Desktop\destilink-framework
2025-05-29 13:57:37,325 - INFO - __main__ - STDIO mode: Using effective_workspace_id 'C:\Users\<USER>\Desktop\destilink-framework'. Database directory will be created on first actual DB use.
2025-05-29 13:57:37,330 - INFO - __main__ - ConPort FastMCP server lifespan starting.
2025-05-29 13:57:37,338 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-05-29 13:57:37,341 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourcesRequest
2025-05-29 13:57:37,342 - INFO - mcp.server.lowlevel.server - Processing request of type ListResourceTemplatesRequest
