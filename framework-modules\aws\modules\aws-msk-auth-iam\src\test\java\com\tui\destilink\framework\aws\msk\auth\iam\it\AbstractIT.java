package com.tui.destilink.framework.aws.msk.auth.iam.it;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.AcknowledgingConsumerAwareMessageListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@Slf4j
@ActiveProfiles({"it"})
@SpringBootTest(classes = {AbstractIT.TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
abstract class AbstractIT {

    public static final String TOPIC_NAME = "bookhub_dev_dead-out_v1";

    //@EnableKafka
    @TestConfiguration
    static class TestConfig {

        protected final AtomicInteger count = new AtomicInteger(0);
        protected final AtomicInteger countAfterAck = new AtomicInteger(0);

        @KafkaListener(topics = {TOPIC_NAME}, concurrency = "1")
        public void listen(ConsumerRecord<String, byte[]> data, Acknowledgment acknowledgment) {
            count.incrementAndGet();
            log.info("Received {}", data);
            countAfterAck.incrementAndGet();
            acknowledgment.acknowledge();
        }

        @Bean
        AcknowledgingConsumerAwareMessageListener<String, byte[]> listener() {
            return new AcknowledgingConsumerAwareMessageListener<String, byte[]>() {
                @Override
                public void onMessage(ConsumerRecord<String, byte[]> data, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
                    System.out.println("onMessage");
                }
            };
        }
    }

    @Autowired
    protected KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    protected TestConfig testConfig;

    @Test
    void test() {
        kafkaTemplate.send(TOPIC_NAME, 0, System.currentTimeMillis(), "hello-key-123", "Hello-World").join();
        kafkaTemplate.send(TOPIC_NAME, 1, "hello-key-123", null).join();
        await().timeout(Duration.ofSeconds(60))
                .pollDelay(Duration.ofSeconds(2))
                .pollInterval(Duration.ofSeconds(1))
                .until(() -> testConfig.count.get() > 0 && testConfig.countAfterAck.get() > 0);
        assertThat(testConfig.count).hasPositiveValue();
        assertThat(testConfig.countAfterAck).hasPositiveValue();
    }
}
