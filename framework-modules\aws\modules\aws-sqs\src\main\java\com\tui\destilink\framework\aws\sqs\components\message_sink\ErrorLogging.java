package com.tui.destilink.framework.aws.sqs.components.message_sink;

import ch.qos.logback.classic.Level;
import com.tui.destilink.framework.core.logging.filter.mute.LogMutingException;
import com.tui.destilink.framework.core.logging.filter.mute.LogMutingMarker;
import com.tui.destilink.framework.core.logging.util.ThrowableUtils;
import io.awspring.cloud.sqs.MessageHeaderUtils;
import org.slf4j.Logger;
import org.springframework.messaging.Message;

import java.util.Collection;

public interface ErrorLogging<T> {

    default Void logError(Throwable t, Message<T> msg, Logger logger, Runnable logSuper) {
        LogMutingException logMutingEx = ThrowableUtils.findThrowableInstanceOf(t, LogMutingException.class);
        if (logMutingEx == null) {
            logSuper.run();
        } else {
            if (logger.isDebugEnabled()) {
                logger.debug(LogMutingMarker.of(Level.ERROR), "Error processing message {}.", MessageHeaderUtils.getId(msg), t);
            }
        }
        return null;
    }

    default Void logError(Throwable t, Collection<Message<T>> msgs, Logger logger, Runnable logSuper) {
        LogMutingException logMutingEx = ThrowableUtils.findThrowableInstanceOf(t, LogMutingException.class);
        if (logMutingEx == null) {
            logSuper.run();
        } else {
            if (logger.isDebugEnabled()) {
                logger.debug(LogMutingMarker.of(Level.ERROR), "Error processing messages {}.", MessageHeaderUtils.getId(msgs), t);
            }
        }
        return null;
    }
}