package com.tui.destilink.framework.aws.sns.logger;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties.SnsLogging;
import com.tui.destilink.framework.aws.sns.util.SnsUtils;
import io.awspring.cloud.sns.core.MessageAttributeDataTypes;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest;

import java.time.Duration;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@ActiveProfiles("kubernetes")
@ExtendWith(OutputCaptureExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
class SnsLogWriterIT {

    private static final String MESSAGE_BODY = "{}";
    private static final String TOPIC = "topic";
    private static final String MESSAGE_ID = "messageId";
    private static final String MESSAGE_GROUP_ID = "gId";
    private static final String MESSAGE_DEDUPLICATION_ID = "dedId";
    private static final String CUSTOM_HEADER_KEY_1 = "headerKey1";
    private static final String CUSTOM_HEADER_VALUE_1 = "headerValue1";

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void shouldWriteContextCorrectly(CapturedOutput output) {
        //given
        SnsLogWriter snsLogWriter = getSnsLogWriter();

        //when
        snsLogWriter.write(MESSAGE_ID, getRequest());

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(output).contains("Message: " + MESSAGE_BODY)
                    .contains("\"aws.sns.topic.arn\":\"" + TOPIC + "\"")
                    .contains("\"aws.sns.messageId\":\"" + MESSAGE_ID + "\"")
                    .contains("\"aws.sns.messageGroupId\":\"" + MESSAGE_GROUP_ID + "\"")
                    .contains("\"aws.sns.messageDeduplicationId\":\"" + MESSAGE_DEDUPLICATION_ID + "\"")
                    .contains("\"aws.sns.headers\":{\"" + CUSTOM_HEADER_KEY_1 + "\":\"" + CUSTOM_HEADER_VALUE_1 + "\"}");
        });
    }

    private PublishRequest getRequest() {
        return PublishRequest.builder()
                .topicArn(TOPIC)
                .messageGroupId(MESSAGE_GROUP_ID)
                .messageDeduplicationId(MESSAGE_DEDUPLICATION_ID)
                .messageAttributes(Map.of(CUSTOM_HEADER_KEY_1,
                        MessageAttributeValue.builder()
                                .dataType(MessageAttributeDataTypes.STRING)
                                .stringValue(CUSTOM_HEADER_VALUE_1)
                                .build()))
                .message(MESSAGE_BODY).build();
    }

    private SnsLogWriter getSnsLogWriter() {
        SnsLogging snsLogging = new SnsLogging();
        snsLogging.setEnabled(true);
        return SnsUtils.getSnsLogWriter(snsLogging, objectMapper);
    }
}