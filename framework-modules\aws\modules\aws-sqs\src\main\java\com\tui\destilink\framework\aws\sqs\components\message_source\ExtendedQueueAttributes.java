package com.tui.destilink.framework.aws.sqs.components.message_source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import io.awspring.cloud.sqs.listener.QueueAttributes;
import io.awspring.cloud.sqs.support.converter.SqsMessageConversionContext;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class ExtendedQueueAttributes extends QueueAttributes {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Getter
    @Jacksonized
    @Builder(toBuilder = true, access = AccessLevel.PROTECTED)
    public static class RedrivePolicy {
        private final int maxReceiveCount;
        private final Arn deadLetterTargetArn;
        private final String deadLetterTargetQueueUrl;
    }

    private final boolean isFifo;
    @Getter
    private final RedrivePolicy redrivePolicy;

    protected ExtendedQueueAttributes(String queueName, String queueUrl, Map<QueueAttributeName, String> attributes, boolean isFifo, RedrivePolicy redrivePolicy) {
        super(queueName, queueUrl, attributes);
        this.isFifo = isFifo;
        this.redrivePolicy = redrivePolicy;
    }

    public boolean isFifo() {
        return isFifo;
    }

    public boolean hasDeadLetterQueue() {
        return redrivePolicy != null;
    }

    @SneakyThrows
    public static ExtendedQueueAttributes of(SqsMessageConversionContext context, QueueUrlResolver queueUrlResolver) {
        return of(Objects.requireNonNull(context.getQueueAttributes()), queueUrlResolver);
    }

    @SneakyThrows
    public static ExtendedQueueAttributes of(QueueAttributes queueAttributes, QueueUrlResolver queueUrlResolver) {
        RedrivePolicy redrivePolicy = parse(queueAttributes.getQueueUrl(), queueAttributes.getQueueAttributes().get(QueueAttributeName.REDRIVE_POLICY), queueUrlResolver);
        boolean isFifo = Optional.ofNullable(queueAttributes.getQueueAttributes().get(QueueAttributeName.FIFO_QUEUE))
                .filter(v -> Boolean.TRUE.toString().equalsIgnoreCase(v)).isPresent();
        return new ExtendedQueueAttributes(queueAttributes.getQueueName(), queueAttributes.getQueueUrl(), queueAttributes.getQueueAttributes(), isFifo, redrivePolicy);
    }

    @SneakyThrows
    private static RedrivePolicy parse(String queueUrl, String value, QueueUrlResolver queueUrlResolver) {
        if (!StringUtils.hasLength(value)) {
            log.warn("No redrive policy configured for SQS queue {}", queueUrl);
            return null;
        }
        RedrivePolicy redrivePolicy = MAPPER.readValue(value, RedrivePolicy.class);
        String dlqQueueUrl = queueUrlResolver.resolveQueueUrlBlocking(redrivePolicy.getDeadLetterTargetArn());
        return redrivePolicy.toBuilder().deadLetterTargetQueueUrl(dlqQueueUrl).build();
    }
}
