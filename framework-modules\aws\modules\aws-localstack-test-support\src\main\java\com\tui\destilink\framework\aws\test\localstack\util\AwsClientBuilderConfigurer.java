package com.tui.destilink.framework.aws.test.localstack.util;

import io.awspring.cloud.autoconfigure.AwsClientProperties;
import io.awspring.cloud.autoconfigure.core.*;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.awscore.client.builder.AwsClientBuilder;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.regions.providers.AwsRegionProvider;

import java.util.Optional;

public class AwsClientBuilderConfigurer {

    private final AwsCredentialsProvider credentialsProvider;
    private final AwsRegionProvider regionProvider;
    private final AwsProperties awsProperties;

    public AwsClientBuilderConfigurer(Binder binder) {
        this.awsProperties = loadAwsProperties(binder);
        this.regionProvider = loadAwsRegionProvider(binder);
        this.credentialsProvider = loadAwsCredentialsProvider(binder, this.regionProvider);
    }

    public static Region resolveRegion(@Nullable AwsClientProperties clientProperties,
                                       AwsRegionProvider regionProvider) {
        return clientProperties != null && StringUtils.hasLength(clientProperties.getRegion())
                ? Region.of(clientProperties.getRegion())
                : regionProvider.getRegion();
    }

    public <T extends AwsClientBuilder<?, ?>> T configure(T builder) {
        return configure(builder, null);
    }

    public <T extends AwsClientBuilder<?, ?>> T configure(T builder, @Nullable AwsClientProperties clientProperties) {
        Assert.notNull(builder, "builder is required");

        builder.credentialsProvider(this.credentialsProvider).region(resolveRegion(clientProperties));
        Optional.ofNullable(this.awsProperties.getEndpoint()).ifPresent(builder::endpointOverride);
        Optional.ofNullable(clientProperties).map(AwsClientProperties::getEndpoint)
                .ifPresent(builder::endpointOverride);

        Optional.ofNullable(this.awsProperties.getDefaultsMode()).ifPresent(builder::defaultsMode);
        Optional.ofNullable(this.awsProperties.getFipsEnabled()).ifPresent(builder::fipsEnabled);
        Optional.ofNullable(this.awsProperties.getDualstackEnabled()).ifPresent(builder::dualstackEnabled);
        return builder;
    }

    public Region resolveRegion(@Nullable AwsClientProperties clientProperties) {
        return resolveRegion(clientProperties, this.regionProvider);
    }

    private AwsProperties loadAwsProperties(Binder binder) {
        return binder.bind(AwsProperties.CONFIG_PREFIX, AwsProperties.class).get();
    }

    private AwsRegionProvider loadAwsRegionProvider(Binder binder) {
        final RegionProperties properties = binder.bind(RegionProperties.PREFIX, RegionProperties.class).get();
        return RegionProviderAutoConfiguration.createRegionProvider(properties);
    }

    private AwsCredentialsProvider loadAwsCredentialsProvider(Binder binder, AwsRegionProvider regionProvider) {
        CredentialsProperties properties = binder.bind(CredentialsProperties.PREFIX, CredentialsProperties.class).get();
        return CredentialsProviderAutoConfiguration.createCredentialsProvider(properties, regionProvider);
    }

}
