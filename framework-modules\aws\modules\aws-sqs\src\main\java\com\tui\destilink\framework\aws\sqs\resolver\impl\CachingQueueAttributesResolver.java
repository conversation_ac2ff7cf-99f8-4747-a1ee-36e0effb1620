package com.tui.destilink.framework.aws.sqs.resolver.impl;

import com.tui.destilink.framework.aws.sqs.resolver.QueueAttributesResolver;
import com.tui.destilink.framework.aws.sqs.resolver.SqsResolveException;
import io.awspring.cloud.sqs.listener.QueueAttributes;
import io.awspring.cloud.sqs.listener.QueueNotFoundStrategy;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Slf4j
public class CachingQueueAttributesResolver extends AbstractResolver<QueueAttributes> implements QueueAttributesResolver {

    public CachingQueueAttributesResolver(SqsAsyncClient sqsAsyncClient) {
        super(sqsAsyncClient);
    }

    @Override
    public CompletableFuture<QueueAttributes> resolveQueueAttributes(String queueUrlArnName) throws SqsResolveException {
        return resolveWithCache(queueUrlArnName)
                .whenComplete(logException("Failed to resolve attributes for SQS queue " + queueUrlArnName))
                .toCompletableFuture();
    }

    @Override
    protected CompletionStage<QueueAttributes> resolveFromSource(String queueUrl) {
        return io.awspring.cloud.sqs.QueueAttributesResolver.builder()
                .sqsAsyncClient(getSqsAsyncClient()).queueName(queueUrl)
                .queueAttributeNames(List.of(QueueAttributeName.ALL))
                .queueNotFoundStrategy(QueueNotFoundStrategy.FAIL).build()
                .resolveQueueAttributes()
                .exceptionally(ex -> {
                    throw new SqsResolveException("Failed to resolve attributes for SQS queue " + queueUrl, extractCauseIfCompetition(ex));
                });
    }
}
