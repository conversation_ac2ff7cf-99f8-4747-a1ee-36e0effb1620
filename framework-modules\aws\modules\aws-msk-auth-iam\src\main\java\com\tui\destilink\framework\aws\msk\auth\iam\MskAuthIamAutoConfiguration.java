package com.tui.destilink.framework.aws.msk.auth.iam;

import com.tui.destilink.framework.aws.msk.auth.iam.config.MskAuthIamProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import static com.tui.destilink.framework.aws.msk.auth.iam.config.MskAuthIamProperties.PREFIX;

@AutoConfiguration
@EnableConfigurationProperties({MskAuthIamProperties.class})
@ConditionalOnProperty(prefix = PREFIX, name = "enabled", havingValue = "true", matchIfMissing = true)
public class MskAuthIamAutoConfiguration {
}
