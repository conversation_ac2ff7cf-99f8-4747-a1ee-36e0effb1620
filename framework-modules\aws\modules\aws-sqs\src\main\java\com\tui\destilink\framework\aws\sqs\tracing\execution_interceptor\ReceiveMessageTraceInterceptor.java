package com.tui.destilink.framework.aws.sqs.tracing.execution_interceptor;

import com.google.common.collect.MapMaker;
import datadog.trace.api.DDTags;
import datadog.trace.api.interceptor.MutableSpan;
import datadog.trace.api.interceptor.TraceInterceptor;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.util.GlobalTracer;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.core.SdkResponse;
import software.amazon.awssdk.core.interceptor.Context;
import software.amazon.awssdk.core.interceptor.ExecutionAttributes;
import software.amazon.awssdk.core.interceptor.ExecutionInterceptor;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentMap;


@Slf4j
public class ReceiveMessageTraceInterceptor implements ExecutionInterceptor {

    static class SqsReceiveMessageFilter implements TraceInterceptor {

        private static final String FILTER_OPERATION_NAME = "aws.http";
        private static final String FILTER_RESOURCE_NAME = "Sqs.ReceiveMessage";

        private static final int PRIORITY = 1000;

        @Override
        public Collection<? extends MutableSpan> onTraceComplete(Collection<? extends MutableSpan> trace) {
            if (trace.size() == 1) {
                MutableSpan span = trace.iterator().next();
                CharSequence operationName = span.getOperationName();
                CharSequence resourceName = span.getResourceName();
                if (operationName != null && operationName.toString().equals(FILTER_OPERATION_NAME)
                        && resourceName != null && resourceName.toString().equals(FILTER_RESOURCE_NAME)) {
                    return List.of();
                }
            }
            return trace;
        }

        @Override
        public int priority() {
            return PRIORITY;
        }
    }

    static {
        datadog.trace.api.GlobalTracer.get().addTraceInterceptor(new SqsReceiveMessageFilter());
    }

    protected static final ConcurrentMap<Message, Span> DATADOG_RECEIVE_ROOT_SPAN_MAP = new MapMaker().weakKeys().makeMap();
    protected static final ConcurrentMap<Message, Span> FW_RECEIVE_ROOT_SPAN_MAP = new MapMaker().weakKeys().makeMap();
    protected static final ConcurrentMap<Message, Scope> FW_RECEIVE_ROOT_SPAN_SCOPE_MAP = new MapMaker().weakKeys().makeMap();

    private static final String OPERATION_NAME = "destilink.framework.sqs.receive";
    private static final String RESOURCE_NAME = "Framework.Sqs.ReceiveMessage";

    @Override
    public void afterExecution(Context.AfterExecution context, ExecutionAttributes executionAttributes) {
        SdkResponse response = context.response();
        if (response instanceof ReceiveMessageResponse receiveMessageResult && receiveMessageResult.messages() != null) {
            for (Message m : receiveMessageResult.messages()) {
                if (GlobalTracer.get().activeSpan() != null) {
                    DATADOG_RECEIVE_ROOT_SPAN_MAP.put(m, GlobalTracer.get().activeSpan());
                }
                // Create child span to keep parent open with dedicated scope
                Span frameworkReceiveSpan = GlobalTracer.get().buildSpan(OPERATION_NAME)
                        .withTag(DDTags.RESOURCE_NAME, RESOURCE_NAME)
                        .start();
                if (frameworkReceiveSpan != null) {
                    FW_RECEIVE_ROOT_SPAN_MAP.put(m, frameworkReceiveSpan);
                    Scope scope = GlobalTracer.get().activateSpan(frameworkReceiveSpan);
                    FW_RECEIVE_ROOT_SPAN_SCOPE_MAP.put(m, scope);
                }
            }
        }
    }
}
