<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.aws</groupId>
        <artifactId>aws</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>aws-msk-auth-iam</artifactId>

    <properties>
        <aws-msk-iam-auth.version>2.3.0</aws-msk-iam-auth.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-core</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.msk</groupId>
            <artifactId>aws-msk-iam-auth</artifactId>
            <version>${aws-msk-iam-auth.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>kafka-test-support</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
