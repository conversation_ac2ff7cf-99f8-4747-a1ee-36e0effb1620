package com.tui.destilink.framework.aws.sns.send.fifo;

import com.tui.destilink.framework.trips.core.logging.TripsContextConstants;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest.Builder;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Slf4j
public class DefaultFifoAttributesEnricher implements FifoAttributesEnricher {

    private static final String BUSINESS_PROCESS_ID = TripsContextConstants.TRIPS_CONTEXT_WITHOUT_PREFIX.get(TripsContextConstants.BUSINESS_PROCESS_ID);
    private static final String EVENT_ID = TripsContextConstants.TRIPS_CONTEXT_WITHOUT_PREFIX.get(TripsContextConstants.EVENT_ID);

    @Override
    public Builder enrichAttributes(Builder builder, Map<String, MessageAttributeValue> attributes) {
        Optional.ofNullable(attributes.get(BUSINESS_PROCESS_ID)).map(MessageAttributeValue::stringValue).ifPresentOrElse(builder::messageGroupId, () -> {
            log.debug("BusinessProcessId is missing, therefore using distinct messageGroupId");
            builder.messageGroupId(UUID.randomUUID().toString());
        });

        Optional.ofNullable(attributes.get(EVENT_ID)).map(MessageAttributeValue::stringValue).ifPresentOrElse(builder::messageDeduplicationId, () -> builder.messageDeduplicationId(UUID.randomUUID().toString()));
        return builder;
    }
}
