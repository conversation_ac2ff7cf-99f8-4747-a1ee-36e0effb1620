package com.tui.destilink.framework.aws.sqs.components;

import com.tui.destilink.framework.aws.sqs.components.message_sink.FwBatchMessageSink;
import com.tui.destilink.framework.aws.sqs.components.message_sink.FwFanOutMessageSink;
import com.tui.destilink.framework.aws.sqs.components.message_source.FwStandardSqsMessageSource;
import com.tui.destilink.framework.aws.sqs.config.DefaultSqsContainerProperties;
import com.tui.destilink.framework.aws.sqs.config.SqsLoggingProperties;
import com.tui.destilink.framework.aws.sqs.logging.SqsLoggingContextCustomizer;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import io.awspring.cloud.sqs.listener.ListenerMode;
import io.awspring.cloud.sqs.listener.SqsContainerOptions;
import io.awspring.cloud.sqs.listener.StandardSqsComponentFactory;
import io.awspring.cloud.sqs.listener.sink.MessageSink;
import io.awspring.cloud.sqs.listener.source.MessageSource;
import org.springframework.beans.factory.ObjectProvider;

public class FwStandardSqsComponentFactory<T> extends StandardSqsComponentFactory<T> {

    private final ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers;
    private final DefaultSqsContainerProperties defaultSqsContainerProperties;
    private final SqsLoggingProperties sqsLoggingProperties;
    private final QueueUrlResolver queueUrlResolver;

    public FwStandardSqsComponentFactory(ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers, DefaultSqsContainerProperties defaultSqsContainerProperties, SqsLoggingProperties sqsLoggingProperties, QueueUrlResolver queueUrlResolver) {
        this.loggingContextCustomizers = loggingContextCustomizers;
        this.defaultSqsContainerProperties = defaultSqsContainerProperties;
        this.sqsLoggingProperties = sqsLoggingProperties;
        this.queueUrlResolver = queueUrlResolver;
    }

    @Override
    public MessageSource<T> createMessageSource(SqsContainerOptions options) {
        return new FwStandardSqsMessageSource<>(loggingContextCustomizers, defaultSqsContainerProperties, sqsLoggingProperties, queueUrlResolver);
    }

    @Override
    public MessageSink<T> createMessageSink(SqsContainerOptions options) {
        return ListenerMode.SINGLE_MESSAGE.equals(options.getListenerMode())
                ? new FwFanOutMessageSink<>()
                : new FwBatchMessageSink<>();
    }
}
