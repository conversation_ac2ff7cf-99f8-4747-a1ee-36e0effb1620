package com.tui.destilink.framework.aws.sqs.components.message_sink;

import io.awspring.cloud.sqs.listener.sink.OrderedMessageSink;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;

import java.util.Collection;

@Slf4j
public class FwOrderedMessageSink<T> extends OrderedMessageSink<T> implements ErrorLogging<T> {

    @Override
    protected Void logError(Throwable t, Message<T> msg) {
        return logError(t, msg, log, () -> super.logError(t, msg));
    }

    @Override
    protected Void logError(Throwable t, Collection<Message<T>> msgs) {
        return logError(t, msgs, log, () -> super.logError(t, msgs));
    }
}
