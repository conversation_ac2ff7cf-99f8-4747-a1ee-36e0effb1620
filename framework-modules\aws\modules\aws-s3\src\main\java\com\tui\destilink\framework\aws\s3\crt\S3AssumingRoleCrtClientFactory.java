package com.tui.destilink.framework.aws.s3.crt;

import io.awspring.cloud.autoconfigure.core.AwsClientBuilderConfigurer;
import io.awspring.cloud.autoconfigure.core.AwsConnectionDetails;
import io.awspring.cloud.autoconfigure.core.AwsProperties;
import io.awspring.cloud.autoconfigure.s3.properties.S3CrtClientProperties;
import io.awspring.cloud.autoconfigure.s3.properties.S3Properties;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.PropertyMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3CrtAsyncClientBuilder;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider;

import java.util.Map;
import java.util.Optional;
import java.util.WeakHashMap;

@Lazy
@Component
public class S3AssumingRoleCrtClientFactory implements InitializingBean {

    private final StsClient stsClient;
    private final S3Properties properties;
    private final AwsProperties awsProperties;
    private final AwsClientBuilderConfigurer awsClientBuilderConfigurer;
    private final AwsConnectionDetails connectionDetails;

    private String roleArnPrefix = "";

    private final String sessionName;

    private final Map<Arn, S3AsyncClient> s3ClientCache = new WeakHashMap<>();

    @Autowired
    public S3AssumingRoleCrtClientFactory(StsClient stsClient, S3Properties properties, AwsProperties awsProperties, AwsClientBuilderConfigurer awsClientBuilderConfigurer, ObjectProvider<AwsConnectionDetails> connectionDetails, @Value("${HOSTNAME:#{null}}") String hostname, @Value("${spring.application.name:unknown}") String appName) {
        this.stsClient = stsClient;
        this.properties = properties;
        this.awsProperties = awsProperties;
        this.awsClientBuilderConfigurer = awsClientBuilderConfigurer;
        this.connectionDetails = connectionDetails.getIfAvailable();
        this.sessionName = hostname != null ? hostname : appName;
    }

    @Override
    public void afterPropertiesSet() {
        this.roleArnPrefix = String.format("arn:aws:iam::%s:role/", stsClient.getCallerIdentity().account());
    }

    public S3AsyncClient instance(String roleName) {
        return instance(Arn.fromString(roleArnPrefix + roleName));
    }

    public S3AsyncClient instance(Arn roleArn) {
        return s3ClientCache.computeIfAbsent(roleArn, k -> {
            Region region = awsClientBuilderConfigurer.resolveRegion(properties, connectionDetails);
            StsAssumeRoleCredentialsProvider credentialsProvider = buildCredentialsProvider(roleArn);
            return buildClient(region, credentialsProvider);
        });
    }

    private S3AsyncClient buildClient(Region region, StsAssumeRoleCredentialsProvider credentialsProvider) {
        S3CrtAsyncClientBuilder builder /* NOSONAR */ = S3AsyncClient.crtBuilder()
                .region(region)
                .credentialsProvider(credentialsProvider);
        Optional.ofNullable(awsProperties.getEndpoint()).ifPresent(builder::endpointOverride);
        Optional.ofNullable(properties.getEndpoint()).ifPresent(builder::endpointOverride);
        Optional.ofNullable(properties.getCrossRegionEnabled()).ifPresent(builder::crossRegionAccessEnabled);
        Optional.ofNullable(properties.getPathStyleAccessEnabled()).ifPresent(builder::forcePathStyle);

        if (properties.getCrt() != null) {
            S3CrtClientProperties crt = properties.getCrt();
            PropertyMapper propertyMapper = PropertyMapper.get();
            propertyMapper.from(crt::getMaxConcurrency).whenNonNull().to(builder::maxConcurrency);
            propertyMapper.from(crt::getTargetThroughputInGbps).whenNonNull().to(builder::targetThroughputInGbps);
            propertyMapper.from(crt::getMinimumPartSizeInBytes).whenNonNull().to(builder::minimumPartSizeInBytes);
            propertyMapper.from(crt::getInitialReadBufferSizeInBytes).whenNonNull()
                    .to(builder::initialReadBufferSizeInBytes);
        }

        return builder.build();
    }

    private StsAssumeRoleCredentialsProvider buildCredentialsProvider(Arn roleArn) {
        return StsAssumeRoleCredentialsProvider.builder()
                .stsClient(stsClient)
                .asyncCredentialUpdateEnabled(true)
                .refreshRequest(b -> b.roleArn(roleArn.toString()).roleSessionName(sessionName))
                .build();
    }
}
