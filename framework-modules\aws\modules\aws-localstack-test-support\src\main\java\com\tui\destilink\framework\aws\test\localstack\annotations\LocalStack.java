package com.tui.destilink.framework.aws.test.localstack.annotations;


import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface LocalStack {

    boolean useNamePrefixes() default true;

    boolean cleanPrefixMatchingResources() default true;

    SqsQueue[] sqsQueues() default {};

    OpenSearchDomain openSearchDomain() default @OpenSearchDomain(name = ""); // Empty name to disable

    SnsTopic[] snsTopics() default {};

    S3Bucket[] s3Buckets() default {};

    SnsSubscription[] snsSubscriptions() default {};
}
