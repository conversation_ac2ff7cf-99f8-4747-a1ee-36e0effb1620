package com.tui.destilink.framework.aws.sqs.listener.retry;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.concurrent.atomic.AtomicBoolean;

@Getter
public class SqsListenerRetryableHandler {

    public static final boolean ALLOW_RETRY_DEFAULT = false;

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public class SqsRetryableScope implements AutoCloseable {
        private final boolean previousValue;

        @Override
        public void close() {
            allowRetry.set(previousValue);
        }
    }

    private final boolean manualHandlingRequired;
    private final int retriesLeft;
    private final boolean retryPossible;

    private final AtomicBoolean allowRetry = new AtomicBoolean(ALLOW_RETRY_DEFAULT);

    public SqsListenerRetryableHandler(boolean manualAckRequired, int retriesLeft) {
        this.manualHandlingRequired = manualAckRequired;
        this.retriesLeft = retriesLeft;
        this.retryPossible = retriesLeft > 0;
    }

    public void allowRetry() {
        allowRetry.set(true);
    }

    public void notAllowRetry() {
        allowRetry.set(false);
    }

    public SqsRetryableScope allowRetryScope() {
        return new SqsRetryableScope(allowRetry.get());
    }
}
