package com.tui.destilink.framework.aws.sqs.listener.retry;

import java.util.function.Supplier;

public interface SqsListenerRetryable {

    void allowRetry();

    void notAllowRetry();

    default <R> R allowRetryOnException(Supplier<R> supplier) {
        try {
            allowRetry();
            R result = supplier.get();
            notAllowRetry();
            return result;
        } catch (Exception ex) {
            allowRetry();
            throw ex;
        }
    }

    default void allowRetryOnException(Runnable runnable) {
        try {
            allowRetry();
            runnable.run();
            notAllowRetry();
        } catch (Exception ex) {
            allowRetry();
            throw ex;
        }
    }

}
