<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework.aws</groupId>
        <artifactId>aws</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>aws-elasticache-redis</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework.aws</groupId>
            <artifactId>aws-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>redis-core</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>ms-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>caching</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
