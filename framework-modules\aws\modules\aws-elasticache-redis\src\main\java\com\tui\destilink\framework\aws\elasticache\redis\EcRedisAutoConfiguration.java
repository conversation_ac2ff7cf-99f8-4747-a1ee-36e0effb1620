package com.tui.destilink.framework.aws.elasticache.redis;

import com.tui.destilink.framework.aws.elasticache.redis.auth.IamAuthTokenRequest;
import com.tui.destilink.framework.aws.elasticache.redis.auth.RedisIamAuthCredentialsProvider;
import com.tui.destilink.framework.aws.elasticache.redis.config.EcRedisProperties;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import io.lettuce.core.RedisCredentialsProvider;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.Delay;
import io.lettuce.core.resource.DirContextDnsResolver;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnThreading;
import org.springframework.boot.autoconfigure.data.redis.ClientResourcesBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisConnectionDetails;
import org.springframework.boot.autoconfigure.thread.Threading;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.connection.lettuce.RedisCredentialsProviderFactory;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.providers.AwsRegionProvider;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.tui.destilink.framework.aws.elasticache.redis.config.EcRedisProperties.PROPERTIES_PREFIX;

@AutoConfiguration
@AutoConfigureBefore(RedisAutoConfiguration.class)
@EnableConfigurationProperties(EcRedisProperties.class)
@ConditionalOnProperty(prefix = PROPERTIES_PREFIX, name = "host")
public class EcRedisAutoConfiguration {

    private final EcRedisProperties redisProps;
    private final ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers;

    @Autowired
    private EcRedisAutoConfiguration(EcRedisProperties redisProps, ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        this.redisProps = redisProps;
        this.builderCustomizers = builderCustomizers;
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisConnectionDetails awsEcRedisConnectionDetails() {
        return new RedisConnectionDetails() {
            @Override
            public String getUsername() {
                return redisProps.getIamAuth().getUserId();
            }

            @Override
            public Cluster getCluster() {
                return () -> List.of(new Node(redisProps.getHost(), redisProps.getPort()));
            }
        };
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisCredentialsProviderFactory redisCredentialsProviderFactory(AwsRegionProvider awsRegionProvider, AwsCredentialsProvider awsCredentialsProvider) {
        return new RedisCredentialsProviderFactory() {
            @Override
            public RedisCredentialsProvider createCredentialsProvider(RedisConfiguration redisConfiguration) {
                final IamAuthTokenRequest iamAuthTokenRequest = new IamAuthTokenRequest(redisProps.getIamAuth().getUserId(),
                        redisProps.getIamAuth().getTokenExpirationSeconds(),
                        redisProps.getIamAuth().getReplicationGroupId(),
                        awsRegionProvider.getRegion());
                return new RedisIamAuthCredentialsProvider(redisProps.getIamAuth().getUserId(), redisProps.getIamAuth().getTokenExpirationSeconds(), awsCredentialsProvider, iamAuthTokenRequest);
            }
        };
    }

    @Bean
    ClientResourcesBuilderCustomizer awsEcRedisClientResourcesBuilderCustomizer() {
        return builder -> builder
                .reconnectDelay(
                        Delay.fullJitter(
                                Duration.ofMillis(100),      // minimum 100 millisecond delay
                                Duration.ofSeconds(10),      // maximum 10 second delay
                                100, TimeUnit.MILLISECONDS)) // 100 millisecond base
                .dnsResolver(new DirContextDnsResolver());   // NOSONAR
    }
    
    @Bean
    @ConditionalOnMissingBean
    public ClusterCommandExecutor clusterCommandExecutor(RedisClusterClient redisClusterClient,
                                                        org.springframework.boot.autoconfigure.data.redis.RedisProperties properties,
                                                        RedisCoreProperties redisCoreProperties) {
        return new ClusterCommandExecutor(redisClusterClient, properties, redisCoreProperties);
    }

    @Bean
    @ConditionalOnMissingBean(RedisConnectionFactory.class)
    @ConditionalOnThreading(Threading.PLATFORM)
    public LettuceConnectionFactory redisConnectionFactory(RedisConnectionDetails redisConnectionDetails, ClientResources clientResources, RedisCredentialsProviderFactory credentialsProviderFactory) {
        return buildLettuceConnectionFactory(redisConnectionDetails, clientResources, credentialsProviderFactory);
    }

    @Bean
    @ConditionalOnMissingBean(RedisConnectionFactory.class)
    @ConditionalOnThreading(Threading.VIRTUAL)
    public LettuceConnectionFactory redisConnectionFactoryVirtualThreads(
            RedisConnectionDetails redisConnectionDetails,
            ClientResources clientResources, RedisCredentialsProviderFactory credentialsProviderFactory) {
        LettuceConnectionFactory factory = buildLettuceConnectionFactory(redisConnectionDetails, clientResources, credentialsProviderFactory);
        SimpleAsyncTaskExecutor executor = new SimpleAsyncTaskExecutor("redis-");
        executor.setVirtualThreads(true);
        factory.setExecutor(executor);
        return factory;
    }

    private LettuceConnectionFactory buildLettuceConnectionFactory(
            RedisConnectionDetails redisConnectionDetails,
            ClientResources clientResources,
            RedisCredentialsProviderFactory credentialsProviderFactory) {
        LettuceConnectionFactory factory = new LettuceConnectionFactory(
                buildRedisClusterConfiguration(redisConnectionDetails),
                buildLettuceClientConfiguration(clientResources, credentialsProviderFactory));
        factory.setShareNativeConnection(false);
        return factory;
    }

    private RedisClusterConfiguration buildRedisClusterConfiguration(RedisConnectionDetails redisConnectionDetails) {
        RedisClusterConfiguration clusterConfiguration = new RedisClusterConfiguration();
        for (RedisConnectionDetails.Node node : redisConnectionDetails.getCluster().getNodes()) {
            clusterConfiguration.addClusterNode(new RedisNode(node.host(), node.port()));
        }
        return clusterConfiguration;
    }

    private LettuceClientConfiguration buildLettuceClientConfiguration(ClientResources clientResources, RedisCredentialsProviderFactory credentialsProviderFactory) {
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder = LettucePoolingClientConfiguration.builder()
                .clientResources(clientResources)
                .clientOptions(ClusterClientOptions.builder().build())
                .redisCredentialsProviderFactory(credentialsProviderFactory)
                .useSsl().disablePeerVerification()
                .and();
        builderCustomizers.orderedStream().forEach(customizer -> customizer.customize(builder));
        return builder.build();
    }
}
