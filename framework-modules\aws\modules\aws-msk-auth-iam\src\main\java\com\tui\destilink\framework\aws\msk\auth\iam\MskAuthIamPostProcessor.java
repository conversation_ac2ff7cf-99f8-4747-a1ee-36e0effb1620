package com.tui.destilink.framework.aws.msk.auth.iam;

import com.tui.destilink.framework.aws.msk.auth.iam.config.MskAuthIamProperties;
import com.tui.destilink.framework.core.properties.AbstractFwPostProcessor;
import io.awspring.cloud.autoconfigure.core.RegionProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertiesPropertySource;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@Slf4j
@Order(MskAuthIamPostProcessor.ORDER)
public class MskAuthIamPostProcessor extends AbstractFwPostProcessor {

    /**
     * Must be executed at latest as possible
     */
    public static final int ORDER = Ordered.LOWEST_PRECEDENCE;

    private static final String PROPERTY_PREFIX = "spring.kafka.properties.";

    private static final String SECURITY_PROTOCOL = "SASL_SSL";

    private static final String SASL_MECHANISM = "AWS_MSK_IAM";

    private static final String JAAS_CONFIG_PREFIX = "software.amazon.msk.auth.iam.IAMLoginModule required";

    private static final String JAAS_AWS_ROLE_ARN = "awsRoleArn";

    private static final String JAAS_AWS_ROLE_SESSION_NAME = "awsRoleSessionName";

    private static final String JAAS_AWS_STS_REGION = "awsStsRegion";

    private static final String JAAS_AWS_DEBUG_CREDS = "awsDebugCreds";

    @Override
    protected void doPostProcessEnvironment(ConfigurableEnvironment env, SpringApplication application) {
        final MskAuthIamProperties mskAuthIamProperties = getMskAuthIamProperties(env);
        if (!Boolean.TRUE.equals(mskAuthIamProperties.getEnabled())) {
            // Skip when not enabled
            return;
        }
        final RegionProperties regionProperties = getRegionProperties(env);
        final String springAppName = getSpringAppName(env);

        final Properties properties = buildProperties(mskAuthIamProperties, regionProperties.getStatic(), springAppName);
        final PropertiesPropertySource propSource = new PropertiesPropertySource(
                buildFwDefaultPropSourceName("mskIamAuthEnvPostProcessor"), properties);

        env.getPropertySources().addFirst(propSource);
    }

    private MskAuthIamProperties getMskAuthIamProperties(Environment env) {
        return Binder.get(env)
                .bindOrCreate(MskAuthIamProperties.PREFIX, MskAuthIamProperties.class);
    }

    private RegionProperties getRegionProperties(Environment env) {
        return Binder.get(env)
                .bindOrCreate(RegionProperties.PREFIX, RegionProperties.class);
    }

    private String getSpringAppName(Environment env) {
        try {
            return env.getRequiredProperty("spring.application.name");
        } catch (IllegalStateException ex) {
            log.error("Spring application name cannot be null", ex);
            throw new IllegalStateException("Spring application cannot be null");
        }
    }

    private String buildPropertyName(String propName) {
        return PROPERTY_PREFIX + propName;
    }

    private Properties buildProperties(MskAuthIamProperties properties, String region, String appName) {
        final Properties propsMap = new Properties();

        propsMap.put(buildPropertyName(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG), SECURITY_PROTOCOL);
        propsMap.put(buildPropertyName(SaslConfigs.SASL_MECHANISM), SASL_MECHANISM);
        propsMap.put(buildPropertyName(SaslConfigs.SASL_JAAS_CONFIG), buildJaasConfig(properties, region, appName));
        propsMap.put(buildPropertyName(SaslConfigs.SASL_CLIENT_CALLBACK_HANDLER_CLASS),
                software.amazon.msk.auth.iam.IAMClientCallbackHandler.class.getCanonicalName());

        return propsMap;
    }

    private String buildJaasConfig(MskAuthIamProperties properties, String region, String appName) {
        final List<String> elements = new ArrayList<>();
        elements.add(JAAS_CONFIG_PREFIX);
        elements.add(JAAS_AWS_ROLE_ARN + "=\"" + properties.getRoleArn() + "\"");
        elements.add(JAAS_AWS_ROLE_SESSION_NAME + "=\"" + appName.substring(0, Math.min(appName.length(), 63)) + "\"");
        if (properties.getSetAwsStsRegion().equals(Boolean.TRUE)) {
            elements.add(JAAS_AWS_STS_REGION + "=\"" + region + "\"");
        }
        elements.add(JAAS_AWS_DEBUG_CREDS + "=" + properties.getDebugCreds());
        return String.join(" ", elements) + ";";
    }

}
