package com.tui.destilink.framework.aws.sqs.resolver;

import software.amazon.awssdk.arns.Arn;

import java.util.concurrent.CompletableFuture;

public interface QueueUrlResolver {

    String resolveQueueUrlBlocking(Arn arn) throws SqsResolveException;

    String resolveQueueUrlBlocking(String queueUrlArnName) throws SqsResolveException;

    CompletableFuture<String> resolveQueueUrl(Arn arn) throws SqsResolveException;

    CompletableFuture<String> resolveQueueUrl(String queueUrlArnName) throws SqsResolveException;
}
