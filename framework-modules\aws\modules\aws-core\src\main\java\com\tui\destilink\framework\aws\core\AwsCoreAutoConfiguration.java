package com.tui.destilink.framework.aws.core;

import com.tui.destilink.framework.aws.core.identity.AwsCallerIdentity;
import io.awspring.cloud.autoconfigure.core.AwsClientBuilderConfigurer;
import io.awspring.cloud.autoconfigure.core.AwsClientCustomizer;
import io.awspring.cloud.autoconfigure.core.CredentialsProviderAutoConfiguration;
import io.awspring.cloud.autoconfigure.core.RegionProviderAutoConfiguration;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.StsClientBuilder;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;

@AutoConfiguration
@AutoConfigureAfter({CredentialsProviderAutoConfiguration.class, RegionProviderAutoConfiguration.class})
public class AwsCoreAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public StsClient defaultStsClient(AwsClientBuilderConfigurer awsClientBuilderConfigurer,
                                      ObjectProvider<AwsClientCustomizer<StsClientBuilder>> configurer) {
        return awsClientBuilderConfigurer
                .configure(StsClient.builder(), null, configurer.getIfAvailable()).build();
    }

    @Bean
    @Lazy
    @ConditionalOnMissingBean
    public AwsCallerIdentity awsCallerIdentity(StsClient stsClient) {
        GetCallerIdentityResponse identity = stsClient.getCallerIdentity();
        return new AwsCallerIdentity(identity.account(), identity.userId(), identity.arn());
    }

}
