package com.tui.destilink.framework.aws.rds.aurora.postgresql.it;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@Slf4j
@ActiveProfiles({"it"})
@SpringBootTest(classes = {TestApplication.class, AbstractAwsAuroraIT.TransConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
abstract class AbstractAwsAuroraIT {

    @Component
    static class TransConfig {

        @Autowired
        private CustomerRepository repository;

        @Transactional
        protected void handle() {
            long start = System.currentTimeMillis();
            try {
                // save a few customers
                repository.save(new CustomerEntity("<PERSON>", "<PERSON>"));
                repository.save(new CustomerEntity("<PERSON>", "<PERSON>'<PERSON>"));
                repository.save(new CustomerEntity("Kim", "Bauer"));
                repository.save(new CustomerEntity("David", "Palmer"));
                repository.save(new CustomerEntity("Michelle", "Dessler"));

                // fetch all customers
                log.info("Customers found with findAll():");
                log.info("-------------------------------");
                repository.findAll().forEach(customer -> {
                    log.info(customer.toString());
                });
                log.info("");

                // fetch an individual customer by ID
                CustomerEntity customer = repository.findById(1L);
                log.info("Customer found with findById(1L):");
                log.info("--------------------------------");
                log.info(customer.toString());
                log.info("");

                // fetch customers by last name
                log.info("Customer found with findByLastName('Bauer'):");
                log.info("--------------------------------------------");
                repository.findByLastName("Bauer").forEach(bauer -> {
                    log.info(bauer.toString());
                });
                log.info("Finished after {}ms", (System.currentTimeMillis() - start));
            } catch (Exception ex) {
                log.error("Failed after {}ms", (System.currentTimeMillis() - start), ex);
                // Fail test
                assertThat(ex).isNull();
            }
        }
    }

    @Autowired
    private TransConfig transConfig;

    @Test
    void test() {
        for (int i = 0; i < 10; i++) {
            await().pollDelay(Duration.ofSeconds(2)).until(() -> true);
            try {
                transConfig.handle();
            } catch (Exception ex) {
                log.error("Transaction Failed", ex);
                // Fail test
                assertThat(ex).isNull();
            }
        }
    }
}
