package com.tui.destilink.framework.aws.sqs.components;

import com.tui.destilink.framework.aws.sqs.components.message_sink.FwBatchMessageSink;
import com.tui.destilink.framework.aws.sqs.components.message_sink.FwOrderedMessageSink;
import com.tui.destilink.framework.aws.sqs.components.message_source.FwFifoSqsMessageSource;
import com.tui.destilink.framework.aws.sqs.config.DefaultSqsContainerProperties;
import com.tui.destilink.framework.aws.sqs.config.SqsLoggingProperties;
import com.tui.destilink.framework.aws.sqs.logging.SqsLoggingContextCustomizer;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import io.awspring.cloud.sqs.MessageHeaderUtils;
import io.awspring.cloud.sqs.listener.*;
import io.awspring.cloud.sqs.listener.sink.MessageSink;
import io.awspring.cloud.sqs.listener.sink.adapter.MessageGroupingSinkAdapter;
import io.awspring.cloud.sqs.listener.sink.adapter.MessageVisibilityExtendingSinkAdapter;
import io.awspring.cloud.sqs.listener.source.MessageSource;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.lang.Nullable;
import org.springframework.messaging.Message;

import java.time.Duration;
import java.util.function.Function;

public class FwFifoSqsComponentFactory<T> extends FifoSqsComponentFactory<T> {

    private final ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers;
    private final DefaultSqsContainerProperties defaultSqsContainerProperties;
    private final SqsLoggingProperties sqsLoggingProperties;
    private final QueueUrlResolver queueUrlResolver;

    public FwFifoSqsComponentFactory(ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers, DefaultSqsContainerProperties defaultSqsContainerProperties, SqsLoggingProperties sqsLoggingProperties, QueueUrlResolver queueUrlResolver) {
        this.loggingContextCustomizers = loggingContextCustomizers;
        this.defaultSqsContainerProperties = defaultSqsContainerProperties;
        this.sqsLoggingProperties = sqsLoggingProperties;
        this.queueUrlResolver = queueUrlResolver;
    }

    @Override
    public MessageSource<T> createMessageSource(SqsContainerOptions options) {
        return new FwFifoSqsMessageSource<>(loggingContextCustomizers, defaultSqsContainerProperties, sqsLoggingProperties, queueUrlResolver);
    }

    @Override
    public MessageSink<T> createMessageSink(SqsContainerOptions options) {
        MessageSink<T> deliverySink = fwCreateDeliverySink(options.getListenerMode());
        MessageSink<T> wrappedDeliverySink = fwMaybeWrapWithVisibilityAdapter(deliverySink,
                options.getMessageVisibility());
        return fwMaybeWrapWithMessageGroupingAdapter(options, wrappedDeliverySink);
    }

    private MessageSink<T> fwMaybeWrapWithMessageGroupingAdapter(SqsContainerOptions options,
                                                                 MessageSink<T> wrappedDeliverySink) {
        return FifoBatchGroupingStrategy.PROCESS_MESSAGE_GROUPS_IN_PARALLEL_BATCHES
                .equals(options.getFifoBatchGroupingStrategy())
                ? new MessageGroupingSinkAdapter<>(wrappedDeliverySink, fwGetMessageGroupingFunction())
                : wrappedDeliverySink;
    }

    private MessageSink<T> fwCreateDeliverySink(ListenerMode listenerMode) {
        return ListenerMode.SINGLE_MESSAGE.equals(listenerMode)
                ? new FwOrderedMessageSink<>()
                : new FwBatchMessageSink<>();
    }

    private MessageSink<T> fwMaybeWrapWithVisibilityAdapter(MessageSink<T> deliverySink, @Nullable Duration messageVisibility) {
        return messageVisibility != null
                ? fwAddMessageVisibilityExtendingSinkAdapter(deliverySink, messageVisibility)
                : deliverySink;
    }

    private MessageVisibilityExtendingSinkAdapter<T> fwAddMessageVisibilityExtendingSinkAdapter(
            MessageSink<T> deliverySink, Duration messageVisibility) {
        MessageVisibilityExtendingSinkAdapter<T> visibilityAdapter = new MessageVisibilityExtendingSinkAdapter<>(
                deliverySink);
        visibilityAdapter.setMessageVisibility(messageVisibility);
        return visibilityAdapter;
    }

    private Function<Message<T>, String> fwGetMessageGroupingFunction() {
        return message -> MessageHeaderUtils.getHeaderAsString(message, SqsHeaders.MessageSystemAttributes.SQS_MESSAGE_GROUP_ID_HEADER);
    }
}
