package com.tui.destilink.framework.aws.elasticache.redis.auth;

import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.signer.Aws4Signer;
import software.amazon.awssdk.auth.signer.params.Aws4PresignerParams;
import software.amazon.awssdk.http.SdkHttpFullRequest;
import software.amazon.awssdk.http.SdkHttpMethod;
import software.amazon.awssdk.regions.Region;

import java.net.URI;
import java.time.Instant;

@RequiredArgsConstructor
public class IamAuthTokenRequest {
    private static final String REQUEST_PROTOCOL = "http://";
    private static final String PARAM_ACTION = "Action";
    private static final String PARAM_USER = "User";
    private static final String ACTION_NAME = "connect";
    private static final String SERVICE_NAME = "elasticache";

    private final String userId;
    private final long tokenExpirationSeconds;
    private final String replicationGroupId;
    private final Region region;

    public String toSignedRequestUri(AwsCredentials credentials) {
        SdkHttpFullRequest request = getSignableRequest();
        SdkHttpFullRequest signedRequest = sign(request, credentials);
        return signedRequest.getUri().toString().replace(REQUEST_PROTOCOL, "");
    }

    private SdkHttpFullRequest getSignableRequest() {
        return SdkHttpFullRequest.builder()
                .method(SdkHttpMethod.GET)
                .uri(getRequestUri())
                .putRawQueryParameter(PARAM_ACTION, ACTION_NAME)
                .putRawQueryParameter(PARAM_USER, userId)
                .build();
    }

    private URI getRequestUri() {
        return URI.create(String.format("%s%s/", REQUEST_PROTOCOL, replicationGroupId));
    }

    private SdkHttpFullRequest sign(SdkHttpFullRequest request, AwsCredentials credentials) {
        // Create an AWS v4 signer
        // Note: While Aws4Signer is marked as deprecated, it's still the recommended approach
        // for presigning requests in the current AWS SDK version
        Aws4Signer signer = Aws4Signer.create(); // NOSONAR migrate in the future

        
        // Build the presigner parameters
        Aws4PresignerParams signerParams = Aws4PresignerParams.builder()
                .signingRegion(region)
                .awsCredentials(credentials)
                .signingName(SERVICE_NAME)
                .expirationTime(Instant.now().plusSeconds(tokenExpirationSeconds))
                .build();

        // Presign the request
        return signer.presign(request, signerParams);
    }
}
