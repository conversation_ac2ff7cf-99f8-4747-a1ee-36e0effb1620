package com.tui.destilink.framework.aws.elasticache.redis.it.ci;

import com.tui.destilink.framework.aws.elasticache.redis.it.TestApplication;
import com.tui.destilink.framework.caching.inject.InjectCache;
import com.tui.destilink.framework.core.util.FutureUtils;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.cluster.TopologyRefreshSynchronizer;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import io.lettuce.core.RedisCommandExecutionException;
import io.lettuce.core.RedisCommandTimeoutException;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.SlotHash;
import io.lettuce.core.cluster.api.async.NodeSelectionServerAsyncCommands;
import io.lettuce.core.cluster.api.async.RedisClusterAsyncCommands;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cache.Cache;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.VirtualThreadTaskExecutor;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;

import static java.util.concurrent.TimeUnit.MINUTES;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.awaitility.Awaitility.await;

@Slf4j
@ActiveProfiles({"ci "})
@SpringBootTest(classes = {TestApplication.class, CIAwsClusterIT.TestConfig.class})
@EnabledIfEnvironmentVariable(named = "CI", matches = "true")
class CIAwsClusterIT {

    @Autowired
    private TopologyRefreshSynchronizer topologyRefreshSynchronizer;

    @TestConfiguration
    static class TestConfig {
        @Bean
        TopologyRefreshSynchronizer topologyRefreshSynchronizer(RedisClusterClient redisClusterClient) {
            return new TopologyRefreshSynchronizer(redisClusterClient);
        }

        @Bean
        ClusterCommandExecutor clusterCommandExecutor(RedisClusterClient redisClusterClient, RedisProperties properties, RedisCoreProperties redisCoreProperties) {
            return new ClusterCommandExecutor(redisClusterClient, properties, redisCoreProperties);
        }
    }

    @Value("${spring.application.name}")
    private String appName;

    @InjectCache("cache1")
    private Cache cache1;

    @Autowired
    private ClusterCommandExecutor clusterCommandExecutor;

    @Test
    void test() {
        assertThat(cache1).isNotNull();
    }

    private final VirtualThreadTaskExecutor vtExecutor = new VirtualThreadTaskExecutor();

    @Test
    void testWriteRead() {
        final int count = 100;
        for (int i = 0; i < count; i++) {
            cache1.put("key-" + i, "Hello Cache " + i);
        }
        for (int i = 0; i < count; i++) {
            assertThat(cache1.get("key-" + i, String.class)).isEqualTo("Hello Cache " + i);
        }
        await().timeout(Duration.ofSeconds(20)).pollDelay(Duration.ofSeconds(10)).until(() -> true);
        for (int i = 0; i < count; i++) {
            assertThat(cache1.get("key-" + i)).isNull();
        }
    }

    @Test
    @DirtiesContext(methodMode = DirtiesContext.MethodMode.BEFORE_METHOD)
    void testTopologyRefreshSynchronizer() {
        assertThat(topologyRefreshSynchronizer).isNotNull();

        long startTime = System.currentTimeMillis();
        await().pollDelay(Duration.ofMillis(100)).until(() -> true); // Wait 100ms
        assertThat(topologyRefreshSynchronizer.timeSinceLastTopologyRefreshMs()).isGreaterThan(startTime);

        CompletableFuture<Void> refresh1 = topologyRefreshSynchronizer.refreshTopology();
        CompletableFuture<Void> ifNecessary1 = topologyRefreshSynchronizer.refreshTopologyIfNecessary(new RedisCommandExecutionException("MOVED"));
        CompletableFuture<Void> refresh2 = topologyRefreshSynchronizer.refreshTopology();
        CompletableFuture<Void> ifNecessary2 = topologyRefreshSynchronizer.refreshTopologyIfNecessary(new RuntimeException("CLUSTERDOWN"));
        CompletableFuture<Void> refresh3 = topologyRefreshSynchronizer.refreshTopology();
        CompletableFuture<Void> ifNecessary3 = topologyRefreshSynchronizer.refreshTopologyIfNecessary(new RedisCommandExecutionException("CLUSTERDOWN"));
        CompletableFuture<Void> refresh4 = topologyRefreshSynchronizer.refreshTopology();
        CompletableFuture<Void> ifNecessary4 = topologyRefreshSynchronizer.refreshTopologyIfNecessary(new RedisCommandTimeoutException());

        assertThat(topologyRefreshSynchronizer.timeSinceLastTopologyRefreshMs()).isBetween(0L, 1000L);

        assertThat(FutureUtils.COMPLETED_FUTURE)
                .isNotEqualTo(refresh1)
                .isEqualTo(ifNecessary2);

        assertThat(refresh1)
                .isEqualTo(refresh2)
                .isEqualTo(refresh3)
                .isEqualTo(refresh4)
                .isEqualTo(ifNecessary1)
                .isNotEqualTo(ifNecessary2)
                .isEqualTo(ifNecessary3)
                .isEqualTo(ifNecessary4);

        assertThatNoException().isThrownBy(refresh1::join);
    }

    @Test
    @Timeout(value = 10, unit = MINUTES)
    void testClusterCommandExecutorSetGetDel() {
        Consumer<Integer> testCode = i -> {
            for (int j = 0; j < 10; j++) {
                final String key = appName + ":" + "test:hello:world:{" + i + "x" + j + "}";
                final String value = "value" + j;
                final int slot = SlotHash.getSlot(key);
                final Function<RedisClusterAsyncCommands<String, String>, RedisFuture<String>> function = c -> {
                    c.set(key, value);
                    var future = c.get(key);
                    c.del(key);
                    return future;
                };
                String result = clusterCommandExecutor.executeInPipelineSyncedOnNode(slot, function)
                        //.thenCompose(redisFuture -> redisFuture)
                        .whenComplete((s, t) -> {
                            if (t != null) {
                                log.error("Failed to set and get key {} and value {} in slot {}", key, value, slot, t);
                            }
                        })
                        .toCompletableFuture()
                        .join();
                assertThat(result).isEqualTo(value);
            }
            log.info("Finished set get and del round {}", i);
        };
        handleClusterCommandExecutorTests(1000, testCode);
    }

    @Test
    @Timeout(value = 5, unit = MINUTES)
    void testClusterCommandExecutorNodeSelection() {
        Consumer<Integer> testCode = i -> {
            assertThatNoException().isThrownBy(() -> clusterCommandExecutor.executeOnAllNodes(NodeSelectionServerAsyncCommands::clientGetname).join());
            assertThatNoException().isThrownBy(() -> clusterCommandExecutor.executeOnAllUpstream(NodeSelectionServerAsyncCommands::clientInfo).join());
            assertThatNoException().isThrownBy(() -> clusterCommandExecutor.executeOnAllReplicas(NodeSelectionServerAsyncCommands::clientId).join());
        };
        handleClusterCommandExecutorTests(1000, testCode);
    }

    void handleClusterCommandExecutorTests(int testCount, Consumer<Integer> testCode) {
        assertThat(clusterCommandExecutor).isNotNull();

        AtomicLong started = new AtomicLong(0);
        AtomicLong done = new AtomicLong(0);
        AtomicLong error = new AtomicLong(0);

        Semaphore semaphore = new Semaphore(0);
        ArrayList<CompletableFuture<Void>> futures = new ArrayList<>(testCount);
        for (int i = 0; i < testCount; i++) {
            final int round = i;
            futures.add(vtExecutor.submitCompletable(() -> {
                try {
                    semaphore.acquireUninterruptibly();
                    started.incrementAndGet();

                    testCode.accept(round);

                    done.incrementAndGet();
                } catch (Exception ex) {
                    error.incrementAndGet();
                    log.error("Exception", ex);
                }
            }));
        }
        semaphore.release(testCount);
        assertThat(futures).hasSize(testCount);
        assertThatNoException().isThrownBy(() -> CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join());
        assertThat(started.get()).isEqualTo(testCount);
        assertThat(done.get()).isEqualTo(testCount);
        assertThat(error.get()).isZero();
    }
}
