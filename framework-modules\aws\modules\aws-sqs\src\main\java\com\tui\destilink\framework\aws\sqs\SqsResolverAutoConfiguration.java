package com.tui.destilink.framework.aws.sqs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sqs.resolver.QueueAttributesResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueDlqUrlResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueTagsResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import com.tui.destilink.framework.aws.sqs.resolver.impl.CachingQueueAttributesResolver;
import com.tui.destilink.framework.aws.sqs.resolver.impl.CachingQueueDlqUrlResolver;
import com.tui.destilink.framework.aws.sqs.resolver.impl.CachingQueueTagsResolver;
import com.tui.destilink.framework.aws.sqs.resolver.impl.CachingQueueUrlResolver;
import io.awspring.cloud.sqs.config.SqsBootstrapConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;

@AutoConfiguration
@ConditionalOnClass({SqsAsyncClient.class, SqsBootstrapConfiguration.class})
@ConditionalOnProperty(name = "spring.cloud.aws.sqs.enabled", havingValue = "true", matchIfMissing = true)
public class SqsResolverAutoConfiguration {

    @Bean
    public QueueAttributesResolver cachingQueueAttributesResolver(SqsAsyncClient sqsAsyncClient) {
        return new CachingQueueAttributesResolver(sqsAsyncClient);
    }

    @Bean
    public QueueDlqUrlResolver cachingQueueRedrivePolicyResolver(SqsAsyncClient sqsAsyncClient, QueueAttributesResolver queueAttributesResolver, ObjectMapper objectMapper) {
        return new CachingQueueDlqUrlResolver(sqsAsyncClient, queueAttributesResolver, objectMapper);
    }

    @Bean
    public QueueTagsResolver cachingQueueTagsResolver(SqsAsyncClient sqsAsyncClient) {
        return new CachingQueueTagsResolver(sqsAsyncClient);
    }

    @Bean
    public QueueUrlResolver cachingQueueUrlResolver(SqsAsyncClient sqsAsyncClient) {
        return new CachingQueueUrlResolver(sqsAsyncClient);
    }
}
