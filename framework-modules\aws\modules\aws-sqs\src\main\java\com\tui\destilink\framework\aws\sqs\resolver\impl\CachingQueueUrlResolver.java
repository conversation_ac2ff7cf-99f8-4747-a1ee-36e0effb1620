package com.tui.destilink.framework.aws.sqs.resolver.impl;

import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import com.tui.destilink.framework.aws.sqs.resolver.SqsResolveException;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;

@Slf4j
public class CachingQueueUrlResolver extends AbstractResolver<String> implements QueueUrlResolver {

    public CachingQueueUrlResolver(SqsAsyncClient sqsAsyncClient) {
        super(sqsAsyncClient);
    }

    @Override
    public String resolveQueueUrlBlocking(Arn arn) throws SqsResolveException {
        return resolveQueueUrlBlocking(arn.toString());
    }

    @Override
    public String resolveQueueUrlBlocking(String queueUrlArnName) throws SqsResolveException {
        try {
            return resolveQueueUrl(queueUrlArnName).join();
        } catch (CompletionException ex) {
            Throwable cause = ex.getCause();
            if (cause instanceof SqsResolveException sre) {
                throw sre;
            }
            throw new SqsResolveException("Failed to resolve URL for SQS queue " + queueUrlArnName, cause);
        }
    }

    @Override
    public CompletableFuture<String> resolveQueueUrl(Arn arn) throws SqsResolveException {
        return resolveQueueUrl(arn.toString());
    }

    @Override
    public CompletableFuture<String> resolveQueueUrl(String queueUrlArnName) throws SqsResolveException {
        return super.resolveQueueUrl(queueUrlArnName)
                .whenComplete(logException("Failed to resolve URL for SQS queue " + queueUrlArnName))
                .toCompletableFuture();
    }

    @Override
    protected CompletionStage<String> resolveFromSource(String queueUrl) {
        return CompletableFuture.failedStage(new UnsupportedOperationException());
    }
}
