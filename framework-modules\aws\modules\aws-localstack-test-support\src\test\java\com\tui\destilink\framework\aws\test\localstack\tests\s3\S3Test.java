package com.tui.destilink.framework.aws.test.localstack.tests.s3;

import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.S3Bucket;
import com.tui.destilink.framework.aws.test.localstack.services.LocalStackS3Service;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.BucketVersioningStatus;
import software.amazon.awssdk.services.s3.model.NoSuchBucketException;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.S3_PROPERTY_PREFIX;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles({"it"})
@LocalStack(
        s3Buckets = {
                @S3Bucket(name = "hello-bucket"),
                @S3Bucket(name = "hello-versioning", versioningEnabled = true)
        }
)
class S3Test {

    @Autowired
    private S3Client s3Client;

    @Value("${" + S3_PROPERTY_PREFIX + "hello-bucket.name}")
    private String bucketName;

    @Value("${" + S3_PROPERTY_PREFIX + "hello-versioning.name}")
    private String versionedBucketName;

    @Autowired
    private LocalStackPropertiesResolver propertiesProvider;

    @Autowired
    private LocalStackS3Service localStackS3Service;

    @Test
    void testPropertiesProvider() {
        assertThat(propertiesProvider.getBucketName("hello-bucket")).isEqualTo(bucketName);
        assertThat(propertiesProvider.getBucketName("hello-versioning")).isEqualTo(versionedBucketName);
    }

    @Test
    void testBucketExist() {
        assertDoesNotThrow(() -> s3Client.getBucketVersioning(b -> b.bucket(bucketName)));
        assertDoesNotThrow(() -> s3Client.getBucketVersioning(b -> b.bucket(versionedBucketName)));

        assertThat(s3Client.getBucketVersioning(b -> b.bucket(bucketName)).status())
                .isNull();

        assertThat(s3Client.getBucketVersioning(b -> b.bucket(versionedBucketName)).status())
                .isEqualTo(BucketVersioningStatus.ENABLED);
    }

    @Test
    @DirtiesContext
    void testBucketsDestroyed() {
        // Make sure both buckets contain files
        assertThat(s3Client.putObject(b -> b.bucket(bucketName).key("/hello/world"), RequestBody.empty()).eTag()).isNotNull();
        assertThat(s3Client.putObject(b -> b.bucket(versionedBucketName).key("/hello/world"), RequestBody.empty()).versionId()).isNotNull();

        localStackS3Service.destroy();

        assertThrows(NoSuchBucketException.class, () -> s3Client.getBucketVersioning(b -> b.bucket(bucketName)));
        assertThrows(NoSuchBucketException.class, () -> s3Client.getBucketVersioning(b -> b.bucket(versionedBucketName)));
    }
}
