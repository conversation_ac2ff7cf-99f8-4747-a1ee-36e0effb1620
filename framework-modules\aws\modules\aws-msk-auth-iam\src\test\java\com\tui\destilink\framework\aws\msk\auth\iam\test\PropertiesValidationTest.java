package com.tui.destilink.framework.aws.msk.auth.iam.test;

import com.tui.destilink.framework.aws.msk.auth.iam.config.MskAuthIamProperties;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class PropertiesValidationTest {

    public static final String VALID_ROLE_ARN = "arn:aws:iam::000000000000:role/hello-world";
    public static final String INVALID_ROLE_ARN = "arn:aws:iam::00000000000:roe/hello-world";

    private final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    @Test
    void validRoleArnTest() {
        MskAuthIamProperties properties = new MskAuthIamProperties();
        properties.setRoleArn(VALID_ROLE_ARN);
        Set<ConstraintViolation<MskAuthIamProperties>> result = validator.validate(properties);
        assertThat(result).isEmpty();
    }

    @Test
    void invalidRoleArnTest() {
        MskAuthIamProperties properties = new MskAuthIamProperties();
        properties.setRoleArn(INVALID_ROLE_ARN);
        Set<ConstraintViolation<MskAuthIamProperties>> result = validator.validate(properties);
        assertThat(result).hasSize(1).allMatch(e -> e.getPropertyPath().toString().equals("roleArn"));
    }

}
