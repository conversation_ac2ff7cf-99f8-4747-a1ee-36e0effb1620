package com.tui.destilink.framework.aws.elasticache.redis.auth;

import com.google.common.base.Suppliers;
import io.lettuce.core.RedisCredentials;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@RequiredArgsConstructor
public class IamRedisCredentials implements RedisCredentials {

    private final String userId;
    private final Supplier<String> iamAuthTokenSupplier;

    public static IamRedisCredentials of(String userId, AwsCredentialsProvider awsCredentialsProvider, IamAuthTokenRequest iamAuthTokenRequest, long tokenExpirationSeconds) {
        return new IamRedisCredentials(userId, Suppliers.memoizeWithExpiration(() -> iamAuthTokenRequest.toSignedRequestUri(awsCredentialsProvider.resolveCredentials()), tokenExpirationSeconds, TimeUnit.SECONDS));
    }

    @Override
    public String getUsername() {
        return userId;
    }

    @Override
    public boolean hasUsername() {
        return StringUtils.isNotEmpty(userId);
    }

    @Override
    public char[] getPassword() {
        final String password = iamAuthTokenSupplier.get();
        return Arrays.copyOf(password.toCharArray(), password.length());
    }

    @Override
    public boolean hasPassword() {
        return StringUtils.isNotEmpty(iamAuthTokenSupplier.get());
    }
}
