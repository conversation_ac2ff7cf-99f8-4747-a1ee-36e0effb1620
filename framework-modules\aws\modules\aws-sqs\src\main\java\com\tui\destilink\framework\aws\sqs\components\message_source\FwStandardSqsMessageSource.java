package com.tui.destilink.framework.aws.sqs.components.message_source;

import com.tui.destilink.framework.aws.sqs.config.DefaultSqsContainerProperties;
import com.tui.destilink.framework.aws.sqs.config.SqsLoggingProperties;
import com.tui.destilink.framework.aws.sqs.logging.SqsLoggingContextCustomizer;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import org.springframework.beans.factory.ObjectProvider;

public class FwStandardSqsMessageSource<T> extends AbstractAsyncErrorHandlingSqsMessageSource<T> {

    public FwStandardSqsMessageSource(ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers, DefaultSqsContainerProperties defaultSqsContainerProperties, SqsLoggingProperties properties, QueueUrlResolver queueUrlResolver) {
        super(loggingContextCustomizers, defaultSqsContainerProperties, properties, queueUrlResolver);
    }
}
