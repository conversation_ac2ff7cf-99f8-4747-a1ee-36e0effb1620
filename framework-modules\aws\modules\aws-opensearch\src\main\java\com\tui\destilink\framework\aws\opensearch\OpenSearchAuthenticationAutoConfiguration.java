package com.tui.destilink.framework.aws.opensearch;

import com.google.common.base.Suppliers;
import io.awspring.cloud.autoconfigure.core.CredentialsProviderAutoConfiguration;
import io.awspring.cloud.autoconfigure.core.RegionProviderAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.spring.boot.autoconfigure.RestClientBuilderCustomizer;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.signer.Aws4Signer;
import software.amazon.awssdk.auth.signer.params.Aws4SignerParams;
import software.amazon.awssdk.regions.providers.AwsRegionProvider;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static com.tui.destilink.framework.aws.opensearch.config.OpenSearchConfigProperties.Authentication.PREFIX;

//https://github.com/aws-samples/opensearch-bootful/blob/main/src/main/java/com/aws/samples/opensearch/config/OpenSearchRestClientConfiguration.java

@Slf4j
@AutoConfiguration(after = {RegionProviderAutoConfiguration.class, CredentialsProviderAutoConfiguration.class})
@ConditionalOnProperty(prefix = PREFIX, name = "enabled")
@ConditionalOnClass({AwsCredentialsProvider.class, AwsRegionProvider.class, Aws4Signer.class /* NOSONAR */})
public class OpenSearchAuthenticationAutoConfiguration {

    public static final String SERVICE_NAME = "es";

    @Bean
    @ConditionalOnBean(value = {AwsCredentialsProvider.class, AwsRegionProvider.class})
    @ConditionalOnMissingBean(name = "awsSigningRestClientBuilderCustomizer")
    public RestClientBuilderCustomizer awsSigningRestClientBuilderCustomizer(
            AwsCredentialsProvider credentialsProvider, AwsRegionProvider regionProvider) {

        Aws4Signer signer = Aws4Signer.create(); // NOSONAR
        Supplier<Aws4SignerParams> params = getParams(credentialsProvider, regionProvider);
        AWSRequestSigningInterceptor interceptor = new AWSRequestSigningInterceptor(signer, params);
        return builder -> builder.setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.addInterceptorLast(interceptor));
    }

    private static Supplier<Aws4SignerParams> getParams(AwsCredentialsProvider credentialsProvider, AwsRegionProvider regionProvider) {
        return Suppliers.memoizeWithExpiration(() -> Aws4SignerParams.builder().signingName(SERVICE_NAME)
                .signingRegion(regionProvider.getRegion())
                .awsCredentials(credentialsProvider.resolveCredentials())
                .build(), 60L, TimeUnit.SECONDS);
    }

}