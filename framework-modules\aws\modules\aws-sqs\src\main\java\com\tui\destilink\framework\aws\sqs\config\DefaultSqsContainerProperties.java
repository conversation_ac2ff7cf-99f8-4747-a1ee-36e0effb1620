package com.tui.destilink.framework.aws.sqs.config;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import org.hibernate.validator.constraints.time.DurationMax;
import org.hibernate.validator.constraints.time.DurationMin;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;

import static com.tui.destilink.framework.aws.sqs.config.DefaultSqsContainerProperties.PREFIX;

@Data
@ConfigurationProperties(prefix = PREFIX, ignoreUnknownFields = false)
@Validated
public class DefaultSqsContainerProperties {

    public static final String PREFIX = "destilink.fw.aws.sqs.container.default";

    @Min(1)
    @Max(Integer.MAX_VALUE)
    private int maxConcurrentMessages = 100;

    @DurationMin(seconds = 0)
    @DurationMax(seconds = 60)
    private Duration listenerShutdownTimeout = Duration.ofSeconds(60);

    @DurationMin(seconds = 0)
    @DurationMax(seconds = 60)
    private Duration acknowledgementShutdownTimeout = Duration.ofSeconds(60);

    @Min(0)
    @Max(Integer.MAX_VALUE)
    private int maxRetriesNoDlq = 2;
}
