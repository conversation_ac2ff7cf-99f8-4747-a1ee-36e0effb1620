package com.tui.destilink.framework.aws.sns.util;

import com.tui.destilink.framework.aws.sns.exception.SnsFrameworkException;
import com.tui.destilink.framework.trips.core.logging.TripsContextConstants;
import io.awspring.cloud.sns.core.MessageAttributeDataTypes;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;

import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

import static java.util.stream.Collectors.toMap;

@Slf4j
@UtilityClass
public class MessageAttributeUtils {

    public static Map<String, MessageAttributeValue> toMessageAttributeMap(Map<String, String> headers, Boolean enrichTripsContext) {
        final Map<String, MessageAttributeValue> messageAttributes = headers.entrySet().stream()
                .collect(toMap(Map.Entry::getKey, e -> getAttributeForString(e.getValue())));

        if (Boolean.TRUE.equals(enrichTripsContext)) {
            enrichTripsContext(messageAttributes);
        }
        if (messageAttributes.size() > 10) {
            throw new SnsFrameworkException("Number of message attributes [" + messageAttributes.size() + "] exceeds the allowed maximum [10].");
        }
        return messageAttributes;
    }

    public static MessageAttributeValue getAttributeForString(String value) {
        return MessageAttributeValue.builder().dataType(MessageAttributeDataTypes.STRING).stringValue(value).build();
    }

    public static Map<String, String> toHeaderMap(Map<String, MessageAttributeValue> attributeValueMap) {
        return attributeValueMap.entrySet()
                .stream()
                .collect(toMap(Map.Entry::getKey, MessageAttributeUtils::getAttributeValueForString, (a, b) -> b, TreeMap::new));
    }

    private static String getAttributeValueForString(Map.Entry<String, MessageAttributeValue> entry) {
        if (!entry.getValue().dataType().equals(MessageAttributeDataTypes.STRING)) {
            log.error("Unsupported MessageAttributeValue '" + entry.getValue().dataType() + "' encountered. Try to serialize as String");
        }
        return entry.getValue().stringValue();
    }

    private static Map<String, MessageAttributeValue> enrichTripsContext(Map<String, MessageAttributeValue> attributes) {
        TripsContextConstants.TRIPS_CONTEXT_WITHOUT_PREFIX.entrySet()
                .stream()
                .filter(entry -> !attributes.containsKey(entry.getValue())) // allows to override trips context with specific values
                .forEach(entry -> Optional.ofNullable(MDC.get(entry.getKey()))
                        .map(MessageAttributeUtils::getAttributeForString)
                        .ifPresent(value -> attributes.put(entry.getValue(), value)));
        return attributes;
    }
}
