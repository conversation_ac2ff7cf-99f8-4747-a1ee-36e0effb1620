package com.tui.destilink.framework.aws.elasticache.redis.it.autoconfiguration;

import com.tui.destilink.framework.aws.elasticache.redis.it.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;

@EnableAutoConfiguration(exclude = {RedisAutoConfiguration.class})
@SpringBootTest(
        classes = {TestApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class SkipAutoConfigurationIT {

    @Autowired(required = false)
    private RedisConnectionFactory connectionFactory;

    @Autowired(required = false)
    private RedisTemplate<?, ?> redisTemplate;

    @Test
    void testDoNotLoadPostProcessor() {
        assertThat(connectionFactory).isNull();
        assertThat(redisTemplate).isNull();
    }

}
