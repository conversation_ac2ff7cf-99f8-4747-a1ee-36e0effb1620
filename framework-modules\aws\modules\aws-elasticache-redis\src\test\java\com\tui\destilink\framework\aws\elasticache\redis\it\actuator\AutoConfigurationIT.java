package com.tui.destilink.framework.aws.elasticache.redis.it.actuator;

import com.tui.destilink.framework.aws.elasticache.redis.it.TestApplication;
import com.tui.destilink.framework.redis.core.config.CommandsTimeoutSource;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.SocketOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.metrics.MicrometerCommandLatencyRecorder;
import io.lettuce.core.protocol.Command;
import io.lettuce.core.protocol.CommandType;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.DirContextDnsResolver;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalManagementPort;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@ActiveProfiles({"ec"})
@SpringBootTest(
        classes = {TestApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class AutoConfigurationIT {

    private static final String CLIENT_NAME = System.getenv().getOrDefault("HOSTNAME", "hey-sexy-service");

    @LocalManagementPort
    private int managementPort;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ClientResources clientResources;

    @Autowired
    private LettuceConnectionFactory connectionFactory;

    @SuppressWarnings("unchecked")
    @Test
    void testHealthCheck() {
        Map<String, Object> response = restTemplate.getForObject("http://localhost:" + managementPort + "/actuator/health", Map.class);
        assertThat(response).containsEntry("status", "DOWN").containsKey("components");
        Map<String, Object> components = (Map<String, Object>) response.get("components");
        assertThat(components).containsKey("redis");
        Map<String, Object> redis = (Map<String, Object>) components.get("redis");
        assertThat(redis).containsEntry("status", "DOWN").containsKey("details");
    }

    @Test
    void testClientResources() {
        assertThat(clientResources).isInstanceOf(DefaultClientResources.class);
        assertThat(clientResources.dnsResolver())
                .isNotNull()
                .isInstanceOf(DirContextDnsResolver.class);
        assertThat(clientResources.reconnectDelay())
                .isNotNull()
                .matches(d -> d.getClass().getName().equals("io.lettuce.core.resource.FullJitterDelay"));
        assertThat(clientResources.commandLatencyRecorder())
                .isNotNull()
                .isInstanceOf(MicrometerCommandLatencyRecorder.class);
    }

    @Test
    void testLettuceConnectionFactory() {
        assertThat(connectionFactory.getShareNativeConnection()).isTrue();
        assertThat(connectionFactory.getClusterConfiguration()).isNotNull();
        assertThat(connectionFactory.getClientName()).isEqualTo(CLIENT_NAME);
        assertThat(connectionFactory.isUseSsl()).isTrue();
        assertThat(connectionFactory.isVerifyPeer()).isFalse();
        assertThat(connectionFactory.getClientConfiguration())
                .isInstanceOf(LettucePoolingClientConfiguration.class)
                .matches(cc -> cc.isUseSsl() && !cc.isVerifyPeer())
                .matches(cc -> cc.getRedisCredentialsProviderFactory().isPresent());
        LettucePoolingClientConfiguration poolingClientConfiguration = (LettucePoolingClientConfiguration) connectionFactory.getClientConfiguration();
        assertThat(poolingClientConfiguration.getPoolConfig())
                .isNotNull();
    }

    @Test
    void testClientOptions() {
        assertThat(connectionFactory.getClientConfiguration().getClientOptions())
                .isPresent()
                .matches(o -> o.orElseThrow() instanceof ClusterClientOptions);
        ClusterClientOptions cco = connectionFactory.getClientConfiguration().getClientOptions()
                .map(ClusterClientOptions.class::cast)
                .orElseThrow();
        assertThat(cco)
                .matches(ClientOptions::isAutoReconnect)
                .matches(c -> !c.isValidateClusterNodeMembership());
        assertThat(cco.getTopologyRefreshOptions())
                .isNotNull()
                .matches(ClusterTopologyRefreshOptions::isPeriodicRefreshEnabled)
                .matches(ClusterTopologyRefreshOptions::useDynamicRefreshSources)
                .matches(o -> !o.getAdaptiveRefreshTriggers().isEmpty());
        assertThat(cco.getSocketOptions())
                .isNotNull()
                .matches(o -> o.getConnectTimeout().equals(Duration.ofSeconds(5)))
                .matches(SocketOptions::isKeepAlive);
        assertThat(cco.getTimeoutOptions().getSource())
                .isNotNull()
                .isInstanceOf(CommandsTimeoutSource.class)
                .matches(s -> s.getTimeout(new Command<>(CommandType.FLUSHDB, null, null)) == Duration.ofSeconds(20).toMillis())
                .matches(s -> s.getTimeout(new Command<>(CommandType.GET, null, null)) == Duration.ofSeconds(10).toMillis());
    }
}
