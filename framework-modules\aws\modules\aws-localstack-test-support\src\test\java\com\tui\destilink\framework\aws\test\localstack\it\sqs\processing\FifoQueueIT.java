package com.tui.destilink.framework.aws.test.localstack.it.sqs.processing;

import io.awspring.cloud.sqs.listener.SqsHeaders;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.messaging.Message;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry;
import software.amazon.awssdk.services.sqs.model.SendMessageBatchResultEntry;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.SqsConsumers.*;
import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.TestMessageConverter.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class FifoQueueIT extends AbstractSqsProcessingIT {

    @Test
    void testQueueFifo(CapturedOutput output) {
        String messageGroupId1 = UUID.randomUUID().toString();
        String messageGroupId2 = UUID.randomUUID().toString();
        List<SendMessageBatchRequestEntry> entriesGroup1 = buildListenerTestMessages(messageGroupId1);
        List<SendMessageBatchRequestEntry> entriesGroup2 = buildListenerTestMessages(messageGroupId2);
        List<SendMessageBatchResultEntry> resultsGroup1 = sendMessages(QUEUE_FIFO_NAME, entriesGroup1);
        List<SendMessageBatchResultEntry> resultsGroup2 = sendMessages(QUEUE_FIFO_NAME, entriesGroup2);
        wait(QUEUE_FIFO_NAME, Duration.ofSeconds(180));

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            resultsGroup1.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
            resultsGroup2.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
        });

        assertThat(getQueueMessageCount(QUEUE_FIFO_NAME)).isZero();
        assertThat(SqsConsumers.RECEIVED_MESSAGES)
                .hasSize(entriesGroup1.size())
                .hasSize(entriesGroup2.size())
                .hasSize(resultsGroup1.size())
                .hasSize(resultsGroup2.size());
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NONE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE)).hasSizeGreaterThanOrEqualTo(4);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)).hasSizeGreaterThanOrEqualTo(4);

        int[] expectedResultIndices = {0, 1, 2, 3, 4, 5, 5, 5, 5, 6, 7, 8, 9, 9, 9, 9};

        int groupIndex1 = 0;
        int groupIndex2 = 0;
        for (Message<?> message : RECEIVED_MESSAGES_ORDERED) {
            if (Objects.requireNonNull(message.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class))
                    .attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID).equals(messageGroupId1)) {
                assertThat(Objects.requireNonNull(message.getHeaders().getId()))
                        .hasToString(resultsGroup1.get(expectedResultIndices[groupIndex1++]).messageId());
            } else {
                assertThat(Objects.requireNonNull(message.getHeaders().getId()))
                        .hasToString(resultsGroup2.get(expectedResultIndices[groupIndex2++]).messageId());
            }
        }
    }

    @Test
    void testQueueDlqFifo(CapturedOutput output) {
        String messageGroupId1 = UUID.randomUUID().toString();
        String messageGroupId2 = UUID.randomUUID().toString();
        List<SendMessageBatchRequestEntry> entriesGroup1 = buildListenerTestMessages(messageGroupId1);
        List<SendMessageBatchRequestEntry> entriesGroup2 = buildListenerTestMessages(messageGroupId2);
        List<SendMessageBatchResultEntry> resultsGroup1 = sendMessages(QUEUE_WITH_DLQ_FIFO_NAME, entriesGroup1);
        List<SendMessageBatchResultEntry> resultsGroup2 = sendMessages(QUEUE_WITH_DLQ_FIFO_NAME, entriesGroup2);
        wait(QUEUE_WITH_DLQ_FIFO_NAME, Duration.ofSeconds(360));

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            resultsGroup1.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
            resultsGroup2.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
        });

        assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isGreaterThanOrEqualTo(4);
        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_FIFO_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_FIFO_NAME)).isEqualTo(8);
        assertThat(SqsConsumers.RECEIVED_MESSAGES)
                .hasSize(entriesGroup1.size())
                .hasSize(entriesGroup2.size())
                .hasSize(resultsGroup1.size())
                .hasSize(resultsGroup2.size());
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NONE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE)).hasSizeGreaterThanOrEqualTo(6);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)).hasSizeGreaterThanOrEqualTo(6);

        int[] expectedResultIndices = {0, 1, 2, 3, 4, 5, 5, 5, 6, 7, 8, 9, 9, 9};

        int groupIndex1 = 0;
        int groupIndex2 = 0;
        for (Message<?> message : RECEIVED_MESSAGES_ORDERED) {
            if (Objects.requireNonNull(message.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class))
                    .attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID).equals(messageGroupId1)) {
                assertThat(Objects.requireNonNull(message.getHeaders().getId()))
                        .hasToString(resultsGroup1.get(expectedResultIndices[groupIndex1++]).messageId());
            } else {
                assertThat(Objects.requireNonNull(message.getHeaders().getId()))
                        .hasToString(resultsGroup2.get(expectedResultIndices[groupIndex2++]).messageId());
            }
        }
    }

    @Test
    void testQueueDlqSingleRetryFifo(CapturedOutput output) {
        String messageGroupId1 = UUID.randomUUID().toString();
        String messageGroupId2 = UUID.randomUUID().toString();
        List<SendMessageBatchRequestEntry> entriesGroup1 = buildListenerTestMessages(messageGroupId1);
        List<SendMessageBatchRequestEntry> entriesGroup2 = buildListenerTestMessages(messageGroupId2);
        List<SendMessageBatchResultEntry> resultsGroup1 = sendMessages(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, entriesGroup1);
        List<SendMessageBatchResultEntry> resultsGroup2 = sendMessages(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, entriesGroup2);
        wait(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, Duration.ofSeconds(360));

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            resultsGroup1.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
            resultsGroup2.stream().map(SendMessageBatchResultEntry::messageId).forEach(
                    id -> assertThat(output.getAll()).contains("Received SQS message id=" + id));
        });

        assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isGreaterThanOrEqualTo(4);
        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME)).isEqualTo(8);
        assertThat(SqsConsumers.RECEIVED_MESSAGES)
                .hasSize(entriesGroup1.size())
                .hasSize(entriesGroup2.size())
                .hasSize(resultsGroup1.size())
                .hasSize(resultsGroup2.size());
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NONE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE)).hasSize(2);

        int[] expectedResultIndices = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};

        int groupIndex1 = 0;
        int groupIndex2 = 0;
        for (Message<?> message : RECEIVED_MESSAGES_ORDERED) {
            if (Objects.requireNonNull(message.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class))
                    .attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID).equals(messageGroupId1)) {
                assertThat(Objects.requireNonNull(message.getHeaders().getId()))
                        .hasToString(resultsGroup1.get(expectedResultIndices[groupIndex1++]).messageId());
            } else {
                assertThat(Objects.requireNonNull(message.getHeaders().getId()))
                        .hasToString(resultsGroup2.get(expectedResultIndices[groupIndex2++]).messageId());
            }
        }
    }

    @Test
    void testConverterQueueFifo(CapturedOutput output) {
        String messageGroupId1 = UUID.randomUUID().toString();
        String messageGroupId2 = UUID.randomUUID().toString();
        List<SendMessageBatchRequestEntry> entriesGroup1 = buildConverterTestMessages(messageGroupId1);
        List<SendMessageBatchRequestEntry> entriesGroup2 = buildConverterTestMessages(messageGroupId2);
        List<SendMessageBatchResultEntry> resultsGroup1 = sendMessages(QUEUE_FIFO_NAME, entriesGroup1);
        List<SendMessageBatchResultEntry> resultsGroup2 = sendMessages(QUEUE_FIFO_NAME, entriesGroup2);
        wait(QUEUE_FIFO_NAME, Duration.ofSeconds(180));
        assertThat(getQueueMessageCount(QUEUE_FIFO_NAME)).isZero();
        assertThat(TestMessageConverter.CONVERTED_MESSAGES)
                .hasSize(entriesGroup1.size())
                .hasSize(entriesGroup2.size())
                .hasSize(resultsGroup1.size())
                .hasSize(resultsGroup2.size());

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Received SQS message id=")).isEqualTo(6);
        });

        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_SUCCESS)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_NULL_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_ERROR_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE)).hasSize(8);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)).hasSize(6);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(6);
    }

    @Test
    void testConverterQueueDlqFifo(CapturedOutput output) {
        String messageGroupId1 = UUID.randomUUID().toString();
        String messageGroupId2 = UUID.randomUUID().toString();
        List<SendMessageBatchRequestEntry> entriesGroup1 = buildConverterTestMessages(messageGroupId1);
        List<SendMessageBatchRequestEntry> entriesGroup2 = buildConverterTestMessages(messageGroupId2);
        List<SendMessageBatchResultEntry> resultsGroup1 = sendMessages(QUEUE_WITH_DLQ_FIFO_NAME, entriesGroup1);
        List<SendMessageBatchResultEntry> resultsGroup2 = sendMessages(QUEUE_WITH_DLQ_FIFO_NAME, entriesGroup2);
        wait(QUEUE_WITH_DLQ_FIFO_NAME, Duration.ofSeconds(180));
        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_FIFO_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_FIFO_NAME)).isEqualTo(10);

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isEqualTo(10);
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Received SQS message id=")).isEqualTo(6);
        });

        assertThat(TestMessageConverter.CONVERTED_MESSAGES)
                .hasSize(entriesGroup1.size())
                .hasSize(entriesGroup2.size())
                .hasSize(resultsGroup1.size())
                .hasSize(resultsGroup2.size());
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_SUCCESS)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_NULL_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_ERROR_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE)).hasSize(6);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)).hasSize(6);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(6);
    }

    @Test
    void testConverterQueueDlqSingleRetryFifo(CapturedOutput output) {
        String messageGroupId1 = UUID.randomUUID().toString();
        String messageGroupId2 = UUID.randomUUID().toString();
        List<SendMessageBatchRequestEntry> entriesGroup1 = buildConverterTestMessages(messageGroupId1);
        List<SendMessageBatchRequestEntry> entriesGroup2 = buildConverterTestMessages(messageGroupId2);
        List<SendMessageBatchResultEntry> resultsGroup1 = sendMessages(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, entriesGroup1);
        List<SendMessageBatchResultEntry> resultsGroup2 = sendMessages(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, entriesGroup2);
        wait(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME, Duration.ofSeconds(180));
        assertThat(getQueueMessageCount(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME)).isZero();
        assertThat(getDlqCount(QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME)).isEqualTo(14);

        await().pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Successfully forwarded message id=")).isEqualTo(14);
            assertThat(StringUtils.countOccurrencesOf(output.getAll(), "Received SQS message id=")).isEqualTo(2);
        });

        assertThat(TestMessageConverter.CONVERTED_MESSAGES)
                .hasSize(entriesGroup1.size())
                .hasSize(entriesGroup2.size())
                .hasSize(resultsGroup1.size())
                .hasSize(resultsGroup2.size());
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_SUCCESS)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_NULL_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_ERROR_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND)).hasSize(2);
        assertThat(TestMessageConverter.CONVERTED_MESSAGES.get(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD)).hasSize(2);
        assertThat(SqsConsumers.RECEIVED_MESSAGES).hasSize(1);
        assertThat(SqsConsumers.RECEIVED_MESSAGES.get(SQS_LISTENER_MESSAGE_ACK)).hasSize(2);
    }
}
