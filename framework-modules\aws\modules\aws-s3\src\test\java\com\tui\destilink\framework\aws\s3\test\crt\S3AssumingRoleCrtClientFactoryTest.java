package com.tui.destilink.framework.aws.s3.test.crt;

import com.tui.destilink.framework.aws.s3.crt.S3AssumingRoleCrtClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@Slf4j
class S3AssumingRoleCrtClientFactoryTest {

    @Test
    void testLazyInitDoesNotThrowException() {
        ConfigurableApplicationContext ctx = SpringApplication.run(TestApplication.class);
        assertThat(ctx).isNotNull();
        BeanCreationException ex = assertThrows(BeanCreationException.class, () -> ctx.getBean(S3AssumingRoleCrtClientFactory.class));
        log.error(ex.getMessage(), ex);
    }
}