package com.tui.destilink.framework.aws.sqs.resolver.impl;

import com.tui.destilink.framework.aws.sqs.resolver.QueueTagsResolver;
import com.tui.destilink.framework.aws.sqs.resolver.SqsResolveException;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.ListQueueTagsResponse;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Slf4j
public class CachingQueueTagsResolver extends AbstractResolver<Map<String, String>> implements QueueTagsResolver {

    public CachingQueueTagsResolver(SqsAsyncClient sqsAsyncClient) {
        super(sqsAsyncClient);
    }

    @Override
    public CompletableFuture<Map<String, String>> resolveQueueTags(String queueUrlArnName) throws SqsResolveException {
        return resolveWithCache(queueUrlArnName)
                .whenComplete(logException("Failed to resolve tags for SQS queue " + queueUrlArnName))
                .toCompletableFuture();
    }

    @Override
    protected CompletionStage<Map<String, String>> resolveFromSource(String queueUrl) {
        return getSqsAsyncClient().listQueueTags(b -> b.queueUrl(queueUrl))
                .thenApply(ListQueueTagsResponse::tags)
                .exceptionally(ex -> {
                    throw new SqsResolveException("Failed to resolve tags for SQS queue " + queueUrl, extractCauseIfCompetition(ex));
                });
    }
}
