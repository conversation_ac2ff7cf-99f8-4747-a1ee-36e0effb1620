package com.tui.destilink.framework.aws.sqs.components.message_source;

import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class AcknowledgementCallbackWrapper<T> implements AcknowledgementCallback<T> {

    private final AcknowledgementCallback<T> delegate;

    private CompletableFuture<Void> onAcknowledgeResult;
    private CompletableFuture<Void> resultFuture;

    public AcknowledgementCallbackWrapper(AcknowledgementCallback<T> delegate) {
        this.delegate = delegate;
    }

    @Override
    public CompletableFuture<Void> onAcknowledge(Message<T> message) {
        return onAcknowledge(Collections.singletonList(message));
    }

    @Override
    public CompletableFuture<Void> onAcknowledge(Collection<Message<T>> messages) {
        if (onAcknowledgeResult != null) {
            return onAcknowledgeResult;
        }
        resultFuture = new CompletableFuture<>();
        onAcknowledgeResult = delegate.onAcknowledge(messages)
                .handle((r, t) -> {
                    if (t == null) {
                        resultFuture.complete(null);
                    } else {
                        resultFuture.completeExceptionally(t);
                    }
                    return null;
                });
        return onAcknowledgeResult;
    }

    public CompletableFuture<Void> getAckResult() {
        return resultFuture;
    }

    @SuppressWarnings("unchecked")
    public CompletableFuture<Void> onAcknowledgeCast(Message<?> message) {
        return onAcknowledge((Message<T>) message);
    }
}
