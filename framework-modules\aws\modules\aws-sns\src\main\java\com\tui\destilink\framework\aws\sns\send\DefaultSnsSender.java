package com.tui.destilink.framework.aws.sns.send;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties;
import com.tui.destilink.framework.aws.sns.logger.SnsLogWriter;
import com.tui.destilink.framework.aws.sns.send.fifo.FifoAttributesEnricher;
import com.tui.destilink.framework.aws.sns.util.MessageAttributeUtils;
import com.tui.destilink.framework.core.tracing.TracingUtils;
import io.awspring.cloud.sns.core.TopicArnResolver;
import io.awspring.cloud.sns.core.TopicNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.awscore.exception.AwsErrorDetails;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;
import software.amazon.awssdk.services.sns.model.SnsException;

import java.util.Map;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
@RequiredArgsConstructor
public class DefaultSnsSender implements SnsSender {

    private final SnsClient snsClient;
    private final TopicArnResolver topicArnResolver;
    private final ObjectMapper objectMapper;
    private final SnsLogWriter snsLogWriter;
    private final FifoAttributesEnricher fifoAttributesEnricher;
    private final DefaultSnsContainerProperties defaultSnsContainerProperties;

    @Override
    public boolean sendMessage(String destination, Object payload, Map<String, String> headers) {
        try {
            String sp = objectMapper.writeValueAsString(payload);
            return sendMessage(destination, sp, headers);
        } catch (JsonProcessingException ex) {
            TracingUtils.setErrorOnSpan(ex, log);
            log.error("Message cannot be serialized", keyValue("aws.sns.topic.name", destination), ex);
            return false;
        }
    }

    @Override
    public boolean sendMessage(String destination, String payload, Map<String, String> headers) {
        String topicArn = null;
        try {
            topicArn = topicArnResolver.resolveTopicArn(destination).toString();
            final PublishRequest publishRequest = prepareRequest(topicArn, payload, headers);
            final PublishResponse publishResponse = snsClient.publish(publishRequest);
            if (publishResponse.sdkHttpResponse().isSuccessful()) {
                snsLogWriter.write(publishResponse.messageId(), publishRequest);
                return true;
            }
            throw SnsException.builder()
                    .message("An unknown error occurred")
                    .statusCode(publishResponse.sdkHttpResponse().statusCode())
                    .requestId(publishResponse.responseMetadata().requestId())
                    .awsErrorDetails(
                            AwsErrorDetails.builder()
                                    .errorMessage("An unknown error occurred")
                                    .serviceName("Sns")
                                    .sdkHttpResponse(publishResponse.sdkHttpResponse())
                                    .build())
                    .build();
        } catch (TopicNotFoundException ex) {
            TracingUtils.setErrorOnSpan(ex, log);
            log.error("SNS Topic {} does not exists or cannot be found due to a missing sns:ListTopics permission", keyValue("aws.sns.topic.name", destination), ex);
        } catch (SnsException ex) {
            TracingUtils.setErrorOnSpan(ex, log);
            log.error("Failed to send message to {} with {}",
                    keyValue("aws.sns.topic.arn", topicArn),
                    keyValue("aws.sns.status", ex.statusCode()),
                    keyValue("aws.sns.message", ex.getMessage()),
                    keyValue("aws.sns.requestId", ex.requestId()));
        }
        return false;
    }

    private PublishRequest prepareRequest(String topicArn, String payload, Map<String, String> headers) {
        final Map<String, MessageAttributeValue> attributes = MessageAttributeUtils.toMessageAttributeMap(headers, defaultSnsContainerProperties.getEnrichTripsContext());
        final PublishRequest.Builder builder = PublishRequest.builder()
                .topicArn(topicArn)
                .message(payload)
                .messageAttributes(attributes);

        if (StringUtils.endsWith(topicArn, ".fifo")) {
            fifoAttributesEnricher.enrichAttributes(builder, attributes);
        }
        return builder.build();
    }
}