spring:
  application:
    name: ci-test-app
  kafka:
    bootstrap-servers: b-1.tuigroup-test-msk-a.ofl0s7.c2.kafka.eu-central-1.amazonaws.com:9098,b-6.tuigroup-test-msk-a.ofl0s7.c2.kafka.eu-central-1.amazonaws.com:9098,b-4.tuigroup-test-msk-a.ofl0s7.c2.kafka.eu-central-1.amazonaws.com:9098
    consumer:
      group-id: bookhub_dev_framework-ci-test
      auto-offset-reset: earliest
      enable-auto-commit: false
      value-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
      isolation-level: read_committed
    listener:
      ack-mode: manual
      missing-topics-fatal: true
      concurrency: 10
destilink:
  fw:
    aws:
      msk:
        auth:
          iam:
            enabled: false