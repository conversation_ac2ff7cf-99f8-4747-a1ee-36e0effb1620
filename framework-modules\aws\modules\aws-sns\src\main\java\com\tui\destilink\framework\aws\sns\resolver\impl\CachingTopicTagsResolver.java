package com.tui.destilink.framework.aws.sns.resolver.impl;

import com.tui.destilink.framework.aws.sns.resolver.TopicTagsResolver;
import io.awspring.cloud.sns.core.TopicArnResolver;
import org.springframework.util.Assert;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.ListTagsForResourceResponse;
import software.amazon.awssdk.services.sns.model.Tag;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CachingTopicTagsResolver implements TopicTagsResolver {

    private final SnsClient snsClient;
    private final TopicArnResolver topicArnResolver;

    private final Map<Arn, List<Tag>> cache = new ConcurrentHashMap<>();

    public CachingTopicTagsResolver(SnsClient snsClient, TopicArnResolver topicArnResolver) {
        Assert.notNull(snsClient, "SnsClient cannot be null!");
        this.snsClient = snsClient;
        this.topicArnResolver = topicArnResolver;
    }

    @Override
    public List<Tag> resolveTopicTags(String topicArnOrName) {
        Arn topicArn = topicArnResolver.resolveTopicArn(topicArnOrName);
        return cache.computeIfAbsent(topicArn, this::resolveTopicTags);
    }

    @Override
    public List<Tag> resolveTopicTags(Arn topicArn) {
        ListTagsForResourceResponse tagsResponse = snsClient.listTagsForResource(b -> b.resourceArn(topicArn.toString()));
        if (tagsResponse.hasTags()) {
            return tagsResponse.tags();
        }
        return List.of();
    }

}
