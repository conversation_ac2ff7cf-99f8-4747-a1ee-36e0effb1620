package com.tui.destilink.framework.aws.sqs.util;

import com.tui.destilink.framework.aws.sqs.components.message_source.AcknowledgementCallbackWrapper;
import com.tui.destilink.framework.aws.sqs.components.message_source.ExtendedQueueAttributes;
import com.tui.destilink.framework.aws.sqs.listener.retry.SqsListenerRetryableHandler;
import io.awspring.cloud.sqs.MessagingHeaders;
import io.awspring.cloud.sqs.listener.SqsHeaders;
import lombok.experimental.UtilityClass;
import org.springframework.messaging.Message;

import java.util.Objects;
import java.util.Optional;

@UtilityClass
public class MessagingMessageHeadersUtils {

    public static final String MESSAGE_HEADER_PREFIX = "Fw_Sqs_";
    public static final String MESSAGE_HEADER_RETRYABLE_HANDLER = MESSAGE_HEADER_PREFIX + "SqsListenerRetryableHandler";

    public static software.amazon.awssdk.services.sqs.model.Message extractSourceMessage(Message<?> message) {
        return Objects.requireNonNull(message.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class));
    }

    @SuppressWarnings("unchecked")
    public static <T> AcknowledgementCallbackWrapper<T> extractAcknowledgementCallbackWrapper(Message<T> message) {
        return Objects.requireNonNull(message.getHeaders().get(MessagingHeaders.ACKNOWLEDGMENT_CALLBACK_HEADER, AcknowledgementCallbackWrapper.class));
    }

    public static SqsListenerRetryableHandler extractSqsListenerRetryable(Message<?> message) {
        return Objects.requireNonNull(message.getHeaders().get(MESSAGE_HEADER_RETRYABLE_HANDLER, SqsListenerRetryableHandler.class));
    }

    public static boolean isFifo(Message<?> message) {
        return extractExtendedQueueAttributes(message).isFifo();
    }

    public static ExtendedQueueAttributes extractExtendedQueueAttributes(Message<?> message) {
        return Objects.requireNonNull(message.getHeaders().get(SqsHeaders.SQS_QUEUE_ATTRIBUTES_HEADER, ExtendedQueueAttributes.class));
    }

    public static boolean hasRedrivePolicy(Message<?> message) {
        return extractRedrivePolicy(message).isPresent();
    }

    public static Optional<ExtendedQueueAttributes.RedrivePolicy> extractRedrivePolicy(Message<?> message) {
        return Optional.ofNullable(extractExtendedQueueAttributes(message).getRedrivePolicy());
    }
}
