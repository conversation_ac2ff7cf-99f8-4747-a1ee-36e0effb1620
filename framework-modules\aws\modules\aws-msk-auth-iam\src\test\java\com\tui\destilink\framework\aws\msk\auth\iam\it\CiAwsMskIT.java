package com.tui.destilink.framework.aws.msk.auth.iam.it;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@ActiveProfiles({"ci-it"})
@EnabledIfEnvironmentVariable(named = "CI", matches = "true")
class CiAwsMskIT extends AbstractIT {
    @Test
    void dummyTest() {
        log.info("Available");
        assertThat(kafkaTemplate).isNotNull();
    }
}
