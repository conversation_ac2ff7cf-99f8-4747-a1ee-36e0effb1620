package com.tui.destilink.framework.aws.test.localstack.annotations;

import java.lang.annotation.*;

@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface OpenSearchDomain {

    String name() default "default";

    /**
     * The version is "latest" by default. All available engine versions can be used
     * e.g. OpenSearch_2.11
     *
     * @return latest as default or any available version like "OpenSearch_2.11"
     */
    String engineVersion() default "latest"; // Can be latest or "OpenSearch_2.11"
}