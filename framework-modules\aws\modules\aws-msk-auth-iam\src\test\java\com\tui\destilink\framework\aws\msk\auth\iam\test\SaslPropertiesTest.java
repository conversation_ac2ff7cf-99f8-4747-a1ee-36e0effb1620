package com.tui.destilink.framework.aws.msk.auth.iam.test;

import com.tui.destilink.framework.aws.msk.auth.iam.MskAuthIamPostProcessor;
import com.tui.destilink.framework.aws.msk.auth.iam.config.MskAuthIamProperties;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.regions.providers.AwsRegionProvider;

import java.util.Properties;

import static org.assertj.core.api.Assertions.assertThat;

class SaslPropertiesTest {

    private final AwsRegionProvider regionProvider = () -> Region.EU_CENTRAL_1;

    @Test
    void testWithStsShortAppName() {
        MskAuthIamProperties properties = new MskAuthIamProperties();
        properties.setRoleArn(PropertiesValidationTest.VALID_ROLE_ARN);

        Properties result = ReflectionTestUtils.invokeMethod(
                new MskAuthIamPostProcessor(),
                "buildProperties",
                properties,
                regionProvider.getRegion().id(),
                "TestAppName"
        );

        checkProperties(result);

        assertThat(result).anySatisfy((k, v) -> {
            assertThat(k).isEqualTo("spring.kafka.properties.sasl.jaas.config");
            assertThat(v).isEqualTo("software.amazon.msk.auth.iam.IAMLoginModule required awsRoleArn=\"arn:aws:iam::000000000000:role/hello-world\" awsRoleSessionName=\"TestAppName\" awsStsRegion=\"eu-central-1\" awsDebugCreds=false;");
        });
    }

    @Test
    void testWithoutStsShortAppName() {
        MskAuthIamProperties properties = new MskAuthIamProperties();
        properties.setRoleArn(PropertiesValidationTest.VALID_ROLE_ARN);
        properties.setSetAwsStsRegion(false);

        Properties result = ReflectionTestUtils.invokeMethod(
                new MskAuthIamPostProcessor(),
                "buildProperties",
                properties,
                regionProvider.getRegion().id(),
                "TestAppName"
        );

        checkProperties(result);

        assertThat(result).anySatisfy((k, v) -> {
            assertThat(k).isEqualTo("spring.kafka.properties.sasl.jaas.config");
            assertThat(v).isEqualTo("software.amazon.msk.auth.iam.IAMLoginModule required awsRoleArn=\"arn:aws:iam::000000000000:role/hello-world\" awsRoleSessionName=\"TestAppName\" awsDebugCreds=false;");
        });
    }

    @Test
    void testWithStsLongAppName() {
        MskAuthIamProperties properties = new MskAuthIamProperties();
        properties.setRoleArn(PropertiesValidationTest.VALID_ROLE_ARN);

        Properties result = ReflectionTestUtils.invokeMethod(
                new MskAuthIamPostProcessor(),
                "buildProperties",
                properties,
                regionProvider.getRegion().id(),
                "ThisIsAVeryLongApplicationNameWithMoreThanSixtyFourCharactersTutTutTut"
        );

        checkProperties(result);

        assertThat(result).anySatisfy((k, v) -> {
            assertThat(k).isEqualTo("spring.kafka.properties.sasl.jaas.config");
            assertThat(v).isEqualTo("software.amazon.msk.auth.iam.IAMLoginModule required awsRoleArn=\"arn:aws:iam::000000000000:role/hello-world\" awsRoleSessionName=\"ThisIsAVeryLongApplicationNameWithMoreThanSixtyFourCharactersTu\" awsStsRegion=\"eu-central-1\" awsDebugCreds=false;");
        });
    }

    private void checkProperties(Properties props) {
        assertThat(props).hasSize(4)
                .anySatisfy((k, v) -> {
                    assertThat(k).isEqualTo("spring.kafka.properties.security.protocol");
                    assertThat(v).isEqualTo("SASL_SSL");
                }).anySatisfy((k, v) -> {
                    assertThat(k).isEqualTo("spring.kafka.properties.sasl.mechanism");
                    assertThat(v).isEqualTo("AWS_MSK_IAM");
                }).anySatisfy((k, v) -> {
                    assertThat(k).isEqualTo("spring.kafka.properties.sasl.client.callback.handler.class");
                    assertThat(v).isEqualTo("software.amazon.msk.auth.iam.IAMClientCallbackHandler");
                });
    }

}
