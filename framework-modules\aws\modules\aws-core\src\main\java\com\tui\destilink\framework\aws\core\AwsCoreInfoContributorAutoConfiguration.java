package com.tui.destilink.framework.aws.core;

import io.awspring.cloud.autoconfigure.core.CredentialsProviderAutoConfiguration;
import io.awspring.cloud.autoconfigure.core.RegionProviderAutoConfiguration;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;

import java.util.HashMap;

@AutoConfiguration
@ConditionalOnClass({InfoContributor.class})
@AutoConfigureAfter({CredentialsProviderAutoConfiguration.class, RegionProviderAutoConfiguration.class})
public class AwsCoreInfoContributorAutoConfiguration {

    @Bean
    public InfoContributor awsIdentityInfoContributor(StsClient stsClient) {
        return builder -> {
            GetCallerIdentityResponse identity = stsClient.getCallerIdentity();

            HashMap<String, Object> details = new HashMap<>();
            HashMap<String, Object> awsDetails = new HashMap<>();
            details.put("aws", awsDetails);
            HashMap<String, Object> identityDetails = new HashMap<>();
            awsDetails.put("identity", identityDetails);
            identityDetails.put("account", identity.account());
            identityDetails.put("arn", identity.arn());
            identityDetails.put("userId", identity.userId());
            builder.withDetails(details);

        };
    }
}
