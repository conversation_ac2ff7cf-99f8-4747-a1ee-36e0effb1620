package com.tui.destilink.framework.aws.test.localstack.tests.sqs;

import com.tui.destilink.framework.aws.sqs.resolver.QueueAttributesResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueDlqUrlResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueTagsResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.SqsQueue;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import io.awspring.cloud.sqs.listener.QueueAttributes;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.QueueDoesNotExistException;

import java.util.Map;
import java.util.concurrent.CompletionException;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.MESSAGING_PROPERTY_PREFIX;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@Slf4j
@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles({"it", "kubernetes"})
@LocalStack(
        sqsQueues = {
                @SqsQueue(name = "hello-world"),
                @SqsQueue(name = "hello-world.fifo", createDlq = true)
        }
)
class SqsResolversTest {

    @Autowired
    private LocalStackPropertiesResolver propertiesProvider;

    @Autowired
    private SqsAsyncClient sqsClient;

    @Autowired
    private QueueUrlResolver queueUrlResolver;

    @Autowired
    private QueueAttributesResolver queueAttributesResolver;

    @Autowired
    private QueueDlqUrlResolver queueDlqUrlResolver;

    @Autowired
    private QueueTagsResolver queueTagsResolver;

    // NO DLQ
    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.name:#{null}}")
    private String helloWorldQueueName;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.arn:#{null}}")
    private String helloWorldQueueArn;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.url:#{null}}")
    private String helloWorldQueueUrl;

    // FIFO With DLQ
    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.fifo.name:#{null}}")
    private String helloWorldFifoQueueName;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.fifo.arn:#{null}}")
    private String helloWorldFifoQueueArn;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.fifo.url:#{null}}")
    private String helloWorldFifoQueueUrl;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.fifo.name:#{null}}")
    private String helloWorldFifoDlqQueueName;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.fifo.arn:#{null}}")
    private String helloWorldFifoDlqQueueArn;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.fifo.url:#{null}}")
    private String helloWorldFifoDlqQueueUrl;

    @Test
    void testQueueUrlResolver() {
        String urlByName = queueUrlResolver.resolveQueueUrl(helloWorldQueueName).join();
        String urlByUrl = queueUrlResolver.resolveQueueUrl(helloWorldQueueUrl).join();
        String urlByArn = queueUrlResolver.resolveQueueUrl(helloWorldQueueArn).join();
        assertThat(urlByName).isEqualTo(urlByUrl).isEqualTo(urlByArn).isEqualTo(helloWorldQueueUrl);

        CompletionException completionException = assertThrows/* NOSONAR */(CompletionException.class, () -> queueUrlResolver.resolveQueueUrl("i-do-not-exist").join());
        assertThat(completionException.getCause()).isInstanceOf(QueueDoesNotExistException.class);
    }

    @Test
    void testQueueAttributesResolver() {
        QueueAttributes attByName = queueAttributesResolver.resolveQueueAttributes(helloWorldQueueName).join();
        QueueAttributes attByUrl = queueAttributesResolver.resolveQueueAttributes(helloWorldQueueUrl).join();
        QueueAttributes attByArn = queueAttributesResolver.resolveQueueAttributes(helloWorldQueueArn).join();

        assertThat(attByName.getQueueName()).isEqualTo(attByUrl.getQueueName()).isEqualTo(attByArn.getQueueName());
        assertThat(attByName.getQueueUrl()).isEqualTo(attByUrl.getQueueUrl()).isEqualTo(attByArn.getQueueUrl());
        assertThat(attByName.getQueueAttributes()).isEqualTo(attByUrl.getQueueAttributes()).isEqualTo(attByArn.getQueueAttributes());
    }

    @Test
    void testQueueDlqUrlResolver() {
        String dlqUrlByName = queueDlqUrlResolver.resolveQueueDlqUrl(helloWorldQueueName).join();
        String dlqUrlByUrl = queueDlqUrlResolver.resolveQueueDlqUrl(helloWorldQueueUrl).join();
        String dlqUrlByArn = queueDlqUrlResolver.resolveQueueDlqUrl(helloWorldQueueArn).join();
        assertThat(dlqUrlByName).isEqualTo(dlqUrlByUrl).isEqualTo(dlqUrlByArn).isNull();

        dlqUrlByName = queueDlqUrlResolver.resolveQueueDlqUrl(helloWorldFifoQueueName).join();
        dlqUrlByUrl = queueDlqUrlResolver.resolveQueueDlqUrl(helloWorldFifoQueueArn).join();
        dlqUrlByArn = queueDlqUrlResolver.resolveQueueDlqUrl(helloWorldFifoQueueUrl).join();
        assertThat(dlqUrlByName).isEqualTo(dlqUrlByUrl).isEqualTo(dlqUrlByArn).isEqualTo(helloWorldFifoDlqQueueUrl);
    }

    @Test
    void testQueueTagsResolver() {
        Map<String, String> tagsByName = queueTagsResolver.resolveQueueTags(helloWorldQueueName).join();
        Map<String, String> tagsByUrl = queueTagsResolver.resolveQueueTags(helloWorldQueueUrl).join();
        Map<String, String> tagsByArn = queueTagsResolver.resolveQueueTags(helloWorldQueueArn).join();
        assertThat(tagsByName).isEqualTo(tagsByUrl).isEqualTo(tagsByArn).isEmpty();

        // Set tag on fifo and non-fifo topics
        sqsClient.tagQueue(b -> b.queueUrl(helloWorldQueueUrl).tags(Map.of("Hello", "Tag")));
        sqsClient.tagQueue(b -> b.queueUrl(helloWorldFifoQueueUrl).tags(Map.of("Hello", "Tag")));

        tagsByName = queueTagsResolver.resolveQueueTags(helloWorldQueueName).join();
        tagsByUrl = queueTagsResolver.resolveQueueTags(helloWorldQueueUrl).join();
        tagsByArn = queueTagsResolver.resolveQueueTags(helloWorldQueueArn).join();
        // Should still be null because values are cached from first try
        assertThat(tagsByName).isEqualTo(tagsByUrl).isEqualTo(tagsByArn).isEmpty();

        tagsByName = queueTagsResolver.resolveQueueTags(helloWorldFifoQueueName).join();
        tagsByUrl = queueTagsResolver.resolveQueueTags(helloWorldFifoQueueUrl).join();
        tagsByArn = queueTagsResolver.resolveQueueTags(helloWorldFifoQueueArn).join();
        assertThat(tagsByName).isEqualTo(tagsByUrl).isEqualTo(tagsByArn).containsEntry("Hello", "Tag").hasSize(1);
    }
}
