package com.tui.destilink.framework.aws.msk.auth.iam.it;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.kafka.test.context.EmbeddedKafka;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@EmbeddedKafka(kraft = true, topics = {AbstractIT.TOPIC_NAME})
class LocalIT extends AbstractIT {
    @Test
    void dummyTest() {
        log.info("Available");
        assertThat(kafkaTemplate).isNotNull();
    }
}
