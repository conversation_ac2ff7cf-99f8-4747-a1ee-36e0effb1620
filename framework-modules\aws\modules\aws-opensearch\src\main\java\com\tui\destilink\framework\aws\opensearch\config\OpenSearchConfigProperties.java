package com.tui.destilink.framework.aws.opensearch.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import static com.tui.destilink.framework.aws.opensearch.config.OpenSearchConfigProperties.PREFIX;

@Data
@Validated
@ConfigurationProperties(prefix = PREFIX, ignoreUnknownFields = false)
public class OpenSearchConfigProperties {

    public static final String PREFIX = "destilink.fw.aws.opensearch";

    @Valid
    @NotNull
    private Authentication auth = new Authentication();

    @Valid
    @NotNull
    private Http http = new Http();

    @Data
    public static class Authentication {

        public static final String PREFIX = OpenSearchConfigProperties.PREFIX + ".auth";

        @NotNull
        private Boolean enabled = true;
    }

    @Data
    public static class Http {

        public static final String PREFIX = OpenSearchConfigProperties.PREFIX + ".http";

        @Valid
        @NotNull
        private Compression compression = new Compression();

        @Data
        public static class Compression {

            public static final String PREFIX = OpenSearchConfigProperties.Http.PREFIX + ".compression";

            @NotNull
            private Boolean enabled = false;
        }
    }

}
