package com.tui.destilink.framework.aws.test.localstack.services;

import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.OpenSearchDomain;
import com.tui.destilink.framework.core.util.FwPropertySourceUtils;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.client.indices.GetIndexResponse;
import org.opensearch.data.client.orhlc.ClientConfiguration;
import org.opensearch.data.client.orhlc.RestClients;
import org.opensearch.spring.boot.autoconfigure.OpenSearchProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.MapPropertySource;
import software.amazon.awssdk.services.opensearch.OpenSearchClient;
import software.amazon.awssdk.services.opensearch.model.*;

import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * Mostly taken from here
 * https://docs.aws.amazon.com/opensearch-service/latest/developerguide/configuration-samples.html
 */
@Slf4j
public class LocalStackOpenSearchService extends AbstractService {

    private static final String OPEN_SEARCH_PROPERTY_NAME = "opensearch";

    private final MapPropertySource fwProperties;

    private final Set<OpenSearchDomain> openSearchDomainSet;
    private final OpenSearchClient client;
    private final List<String> setupDomains = new LinkedList<>();

    private boolean initialized = false;

    public LocalStackOpenSearchService(LocalStack localStack, Set<OpenSearchDomain> openSearchDomainSet,
                                       ConfigurableApplicationContext context,
                                       String resourcePrefix) {
        // need a starting letter or else: Member must satisfy regular expression pattern: [a-z][a-z0-9\-]+
        super(localStack, context, resourcePrefix);
        this.fwProperties = FwPropertySourceUtils.getFwPropertiesOverridePropertySource(context.getEnvironment());
        this.openSearchDomainSet = openSearchDomainSet;
        client = configureClientBuilder(null, null, OpenSearchClient.builder()).build();
    }

    @Override
    public void destroy() {
        if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
            cleanUpPrefixedIndices();
            client.listDomainNames(ListDomainNamesRequest.builder().build())
                    .domainNames().stream()
                    .filter(name -> name.domainName().contains(getResourcePrefix()))
                    .forEach(e -> deleteDomain(client, e.domainName()));
        } else {
            for (String domain : setupDomains) {
                deleteDomain(client, domain);
            }
        }
        initialized = false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
            this.destroy();
        }

        if (initialized || (openSearchDomainSet.isEmpty())) {
            return;
        }

        initialized = true;
        List<OpenSearchDomain> list = openSearchDomainSet.stream().toList();
        for (OpenSearchDomain index : list) {
            // need a starting letter or else: Member must satisfy regular expression pattern: [a-z][a-z0-9\-]+
            String prefixedName = "a-" + generateResourceName(index.name());
            deleteDomain(client, prefixedName);
            createDomain(client, index.engineVersion(), prefixedName);
            setupDomains.add(prefixedName);

            DescribeDomainResponse domain = client.describeDomain(DescribeDomainRequest.builder().domainName(prefixedName).build());
            DomainStatus domainStatus = domain.domainStatus();
            int pathSeparator = domainStatus.endpoint().indexOf("/");
            if (pathSeparator == -1) {
                fwProperties.getSource().put(OPEN_SEARCH_PROPERTY_NAME + ".uris", List.of(domainStatus.endpoint()));
            } else {
                fwProperties.getSource().put(OPEN_SEARCH_PROPERTY_NAME + ".uris", List.of(domainStatus.endpoint().substring(0, pathSeparator)));
                fwProperties.getSource().put(OPEN_SEARCH_PROPERTY_NAME + ".pathPrefix", domainStatus.endpoint().substring(pathSeparator));
            }
            waitForDomainProcessing(client, prefixedName);
            if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
                // Cleanup prefixes before test
                cleanUpPrefixedIndices();
            }
        }
    }

    private void cleanUpPrefixedIndices() {
        OpenSearchProperties props = bindProperties(OPEN_SEARCH_PROPERTY_NAME, OpenSearchProperties.class);
        for (String uri : props.getUris()) {
            try (RestClients.OpenSearchRestClient restClient = buildRestClient(uri, props.getPathPrefix())) {
                GetIndexRequest request = new GetIndexRequest("*");
                GetIndexResponse response = restClient.rest().indices().get(request, RequestOptions.DEFAULT);

                Arrays.stream(response.getIndices())
                        .filter(index -> index.contains(getResourcePrefix()))
                        .forEach(index -> cleanUpIndices(restClient.rest(), index));
            } catch (Exception e) {
                log.error("Failed to cleanup indices for uri {}", uri, e);
            }
        }
    }

    private void cleanUpIndices(RestHighLevelClient highLevelClient, String... indexNames) {
        try {
            DeleteIndexRequest req = new DeleteIndexRequest(indexNames);
            highLevelClient.indices().delete(req, RequestOptions.DEFAULT);
        } catch (Exception ex) {
            log.error("Failed to delete indices {}", indexNames, ex);
        }
    }

    private RestClients.OpenSearchRestClient buildRestClient(String uri, String pathPrefix) {
        ClientConfiguration.TerminalClientConfigurationBuilder builder = ClientConfiguration.builder()
                .connectedTo(uri);
        if (pathPrefix != null && !pathPrefix.isBlank()) {
            return RestClients.create(builder.withPathPrefix(pathPrefix).build());
        }
        return RestClients.create(builder.build());
    }

    /**
     * Creates an Amazon OpenSearch Service domain with the specified options.
     * Some options require other Amazon Web Services resources, such as an Amazon Cognito user pool
     * and identity pool, whereas others require just an instance type or instance
     * count.
     *
     * @param client     The client to use for the requests to Amazon OpenSearch Service
     * @param domainName The name of the domain you want to create
     */

    public static void createDomain(OpenSearchClient client, String version, String domainName) {

        // Create the request and set the desired configuration options

        try {

            // Many instance types require EBS storage.

            CreateDomainRequest createRequest = CreateDomainRequest.builder()
                    .domainName(domainName)
                    .engineVersion(verifyVersion(client, version))
                    .clusterConfig(e -> e
                            .dedicatedMasterEnabled(true)
                            .dedicatedMasterCount(3)
                            // Small, inexpensive instance types for testing. Not recommended for production.
                            .dedicatedMasterType("t2.small.search")
                            .instanceType("t2.small.search")
                            .instanceCount(5)
                            .build())
                    .ebsOptions(e -> e
                            .ebsEnabled(true)
                            .volumeSize(10)
                            .volumeType("gp2")
                            .build())
                    .nodeToNodeEncryptionOptions(e -> e
                            .enabled(true)
                            .build())
                    // You can uncomment this line and add your account ID, a username, and the
                    // domain name to add an access policy.
                    // .accessPolicies("{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"arn:aws:iam::************:user/user-name\"]},\"Action\":[\"es:*\"],\"Resource\":\"arn:aws:es:region:************:domain/domain-name/*\"}]}")
                    .build();

            // Make the request.
            log.info("Sending domain creation request...");
            CreateDomainResponse createResponse = client.createDomain(createRequest);
            log.info("Domain status: " + createResponse.domainStatus().toString());
            log.info("Domain ID: " + createResponse.domainStatus().domainId());
        } catch (OpenSearchException e) {
            log.error(e.awsErrorDetails().errorMessage());
            System.exit(1);
        }
    }

    /**
     * Deletes an Amazon OpenSearch Service domain. Deleting a domain can take
     * several minutes.
     *
     * @param client     The client to use for the requests to Amazon OpenSearch Service
     * @param domainName The name of the domain that you want to delete
     */

    public static void deleteDomain(OpenSearchClient client, String domainName) {

        try {
            DeleteDomainRequest deleteRequest = DeleteDomainRequest.builder().domainName(domainName).build();

            log.info("Sending domain deletion request...");
            DeleteDomainResponse deleteResponse = client.deleteDomain(deleteRequest);
            log.info("Domain status: " + deleteResponse.toString());

        } catch (ResourceNotFoundException e) {
            log.debug("Domain {} not found", domainName);
        } catch (OpenSearchException e) {
            log.error(e.awsErrorDetails().errorMessage());
            System.exit(1);
        }
    }

    /**
     * Waits for the domain to finish processing changes. New domains typically take 15-30 minutes
     * to initialize, but can take longer depending on the configuration. Most updates to existing domains
     * take a similar amount of time. This method checks every 15 seconds and finishes only when
     * the domain's processing status changes to false.
     *
     * @param client     The client to use for the requests to Amazon OpenSearch Service
     * @param domainName The name of the domain that you want to check
     */

    public static void waitForDomainProcessing(OpenSearchClient client, String domainName) throws InterruptedException {
        // Create a new request to check the domain status.
        DescribeDomainRequest describeRequest = DescribeDomainRequest.builder().domainName(domainName).build();

        // Every 15 seconds, check whether the domain is processing.
        DescribeDomainResponse describeResponse = client.describeDomain(describeRequest);
        while (Boolean.TRUE.equals(describeResponse.domainStatus().processing())) {
            log.info("Domain still processing...");
            TimeUnit.SECONDS.sleep(15);
            describeResponse = client.describeDomain(describeRequest);
        }

        // Once we exit that loop, the domain is available
        log.info("Amazon OpenSearch Service has finished processing changes for your domain.");
        log.info("Domain description: " + describeResponse);
    }

    public static String verifyVersion(OpenSearchClient client, String version) {
        ListVersionsResponse availableVersions = client.listVersions(c -> c.maxResults(1000));
        if (version.equalsIgnoreCase("latest")) {
            String latestVersion = availableVersions.versions().getFirst();
            log.info("Deploying OpenSearch Domain with latest version {}", latestVersion);
            return latestVersion;
        }
        Optional<String> resolvedVersion = availableVersions.versions().stream()
                .filter(v -> v.equalsIgnoreCase(version)).findFirst();
        if (resolvedVersion.isEmpty()) {
            log.error("Did not find available OpenSearch version {} in list of available versions {}", version, availableVersions.versions());
            throw new IllegalArgumentException("Did not find OpenSearch version " + version);
        }
        return resolvedVersion.get();
    }
}
