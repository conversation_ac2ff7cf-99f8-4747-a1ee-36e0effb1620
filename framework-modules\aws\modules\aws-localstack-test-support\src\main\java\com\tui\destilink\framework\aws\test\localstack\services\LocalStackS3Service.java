package com.tui.destilink.framework.aws.test.localstack.services;

import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.S3Bucket;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import com.tui.destilink.framework.test.support.core.util.TestSupportPropertiesUtils;
import io.awspring.cloud.autoconfigure.s3.properties.S3Properties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ConfigurableApplicationContext;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.model.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.S3;

@Slf4j
public class LocalStackS3Service extends AbstractService {

    public static final String CLOUDEVENTS_BUCKET_NAME = "cloudevents-bucket";

    private final TestSupportPropertiesUtils s3TestSupportPropsUtils;

    private final Set<S3Bucket> s3Buckets;
    private final boolean createCloudEventBucket;

    private final S3Client s3Client;
    private final Set<String> initializedBuckets = new HashSet<>();
    private boolean initialized = false;

    public LocalStackS3Service(LocalStack localStack, Set<S3Bucket> s3Buckets, boolean createCloudEventBucket, ConfigurableApplicationContext context, String resourcePrefix) {
        super(localStack, context, resourcePrefix);
        this.s3TestSupportPropsUtils = buildTestSupportPropUtils(context, S3);
        this.s3Buckets = s3Buckets;
        this.createCloudEventBucket = createCloudEventBucket;
        this.s3Client = buildS3Client();
    }

    @Override
    public void afterPropertiesSet() {
        if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
            this.destroy();
        }

        if (initialized) {
            return;
        }

        initialized = true;
        for (S3Bucket s3Bucket : s3Buckets) {
            final String bucketName = createBucket(s3Bucket);
            initializedBuckets.add(bucketName);
            s3TestSupportPropsUtils.addProperty(bucketName, s3Bucket.name(), LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_NAME);
        }
        if (createCloudEventBucket) {
            final String bucketName = createBucket(CLOUDEVENTS_BUCKET_NAME, false);
            initializedBuckets.add(bucketName);
            s3TestSupportPropsUtils.addProperty(bucketName, CLOUDEVENTS_BUCKET_NAME, LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_NAME);
        }
    }

    @Override
    public void destroy() {
        if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
            List<Bucket> buckets = this.s3Client.listBuckets().buckets();
            for (Bucket bucket : buckets) {
                if (bucket.name().startsWith(getResourcePrefix())) {
                    deleteBucket(bucket.name());
                }
            }
        } else if (initialized) {
            for (String name : initializedBuckets) {
                deleteBucket(name);
            }
        }
        initialized = false;
    }

    private S3Client buildS3Client() {
        S3ClientBuilder builder = configureClientBuilder(S3Properties.PREFIX, S3Properties.class, S3Client.builder());
        builder.serviceConfiguration(loadProperties(S3Properties.PREFIX, S3Properties.class).toS3Configuration());
        return builder.build();
    }

    private String createBucket(S3Bucket s3Bucket) {
        return createBucket(s3Bucket.name(), s3Bucket.versioningEnabled());
    }

    private String createBucket(String name, boolean versioningEnabled) {
        final String s3BucketName = generateResourceName(name);
        this.s3Client.createBucket(b -> b.bucket(s3BucketName));
        if (versioningEnabled) {
            this.s3Client.putBucketVersioning(b ->
                    b.bucket(s3BucketName).versioningConfiguration(v ->
                            v.status(BucketVersioningStatus.ENABLED)));
        }
        return s3BucketName;
    }

    private void deleteBucket(String name) {
        try {
            log.info("Deleting bucket {}", name);
            while (true) { //NOSONAR
                ListObjectsV2Response listObjectsV2Response = this.s3Client.listObjectsV2(b -> b.bucket(name));
                if (!listObjectsV2Response.hasContents()) {
                    break;
                }
                List<ObjectIdentifier> objectIdentifiers = listObjectsV2Response.contents().stream()
                        .map(S3Object::key)
                        .map(k -> ObjectIdentifier.builder().key(k).build())
                        .toList();
                this.s3Client.deleteObjects(b -> b.bucket(name).delete(d -> d.objects(objectIdentifiers)));
                if (Boolean.FALSE.equals(listObjectsV2Response.isTruncated())) {
                    break;
                }
            }

            while (true) { //NOSONAR
                ListObjectVersionsResponse listObjectVersionsResponse = this.s3Client.listObjectVersions(b -> b.bucket(name));
                if (!listObjectVersionsResponse.hasVersions() && !listObjectVersionsResponse.hasDeleteMarkers()) {
                    break;
                }

                Stream<ObjectIdentifier> versions = listObjectVersionsResponse.versions().stream() // NOSONAR
                        .map(v -> ObjectIdentifier.builder().key(v.key()).versionId(v.versionId()))
                        .map(ObjectIdentifier.Builder::build);

                Stream<ObjectIdentifier> deleteMarkers = listObjectVersionsResponse.deleteMarkers().stream() //NOSONAR
                        .map(m -> ObjectIdentifier.builder()
                                .key(m.key())
                                .versionId(m.versionId()))
                        .map(ObjectIdentifier.Builder::build);

                this.s3Client.deleteObjects(b -> b.bucket(name).delete(d -> d.objects(Stream.concat(versions, deleteMarkers).toList())));
                if (Boolean.FALSE.equals(listObjectVersionsResponse.isTruncated())) {
                    break;
                }
            }

            this.s3Client.deleteBucket(b -> b.bucket(name));

        } catch (Exception ex) {
            log.error("Failed to delete bucket {}", name, ex);
        }
    }
}
