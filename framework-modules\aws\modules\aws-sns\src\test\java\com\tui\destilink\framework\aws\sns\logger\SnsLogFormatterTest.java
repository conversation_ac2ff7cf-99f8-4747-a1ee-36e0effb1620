package com.tui.destilink.framework.aws.sns.logger;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.SnsAutoConfiguration;
import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties.SnsLogging;
import com.tui.destilink.framework.aws.sns.util.SnsUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import software.amazon.awssdk.services.sns.model.PublishRequest;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@EnableAutoConfiguration(exclude = {io.awspring.cloud.autoconfigure.sns.SnsAutoConfiguration.class, SnsAutoConfiguration.class})
class SnsLogFormatterTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void givenSnsMessageShouldFormatPrettyCorrectly() {
        //given
        Map<String, String> headers = new HashMap<>();
        headers.put("eventId", "123");

        String topic = "topic";
        String body = "{\"name\":\"Bookhub\"}";
        String messageId = "id";
        final PublishRequest request = PublishRequest.builder().topicArn(topic).message(body).build();

        //when
        String formattedMessage = getSnsLogFormatter(true).format(messageId, headers, request);

        //then
        assertThat(formattedMessage).isEqualToNormalizingNewlines(
                """
                        SNS Message to Topic topic
                        
                        MessageId: id
                        
                        Headers:\s
                        eventId: 123
                        
                        Message: {
                          "name" : "Bookhub"
                        }""");
    }

    @Test
    void givenSnsMessageShouldFormatNormalCorrectly() {
        //given
        Map<String, String> headers = new HashMap<>();
        headers.put("eventId", "123");

        String topic = "topic";
        String body = "{\"name\":\"Bookhub\"}";
        String messageId = "id";
        final PublishRequest request = PublishRequest.builder().topicArn(topic).message(body).messageGroupId("1234").messageDeduplicationId("5678").build();

        //when
        String formattedMessage = getSnsLogFormatter(false).format(messageId, headers, request);

        //then
        assertThat(formattedMessage).isEqualTo(
                """
                        SNS Message to Topic topic
                        
                        MessageId: id
                        MessageGroupId: 1234
                        MessageDeduplicationId: 5678
                        
                        Headers:\s
                        eventId: 123
                        
                        Message: {"name":"Bookhub"}""");
    }

    private SnsLogFormatter getSnsLogFormatter(boolean pretty) {
        final SnsLogging snsLogging = new SnsLogging();
        snsLogging.setPrettyPrint(pretty);
        return SnsUtils.getSnsLogFormatter(snsLogging, objectMapper);
    }
}