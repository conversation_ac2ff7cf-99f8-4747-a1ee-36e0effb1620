package com.tui.destilink.framework.aws.test.localstack.tests.sns;

import com.tui.destilink.framework.aws.sns.resolver.TopicTagsResolver;
import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.SnsSubscription;
import com.tui.destilink.framework.aws.test.localstack.annotations.SnsTopic;
import com.tui.destilink.framework.aws.test.localstack.annotations.SqsQueue;
import com.tui.destilink.framework.aws.test.localstack.services.LocalStackMessagingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.Tag;

import java.time.Duration;
import java.util.UUID;

import static com.tui.destilink.framework.aws.test.localstack.services.LocalStackS3Service.CLOUDEVENTS_BUCKET_NAME;
import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.MESSAGING_PROPERTY_PREFIX;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@SpringBootTest(classes = {TestApplication.class})
@Import({ListenerConfiguration.class})
@ActiveProfiles({"it"})
@LocalStack(
        sqsQueues = {
                @SqsQueue(name = "hello-queue"),
                @SqsQueue(name = "hello-queue.fifo")
        },
        snsTopics = {
                @SnsTopic(name = "hello-topic"),
                @SnsTopic(name = "hello-topic.fifo"),
                @SnsTopic(name = "cloud-event-topic", isCloudEvent = true),
                @SnsTopic(name = "cloud-event-topic.fifo", isCloudEvent = true)
        },
        snsSubscriptions = {
                @SnsSubscription(snsTopicName = "hello-topic", sqsQueueName = "hello-queue"),
                @SnsSubscription(snsTopicName = "hello-topic.fifo", sqsQueueName = "hello-queue.fifo")
        }
)
class SnsWithSubscriptionTest {

    private static final String TEST_MESSAGE = "Hello World";
    private static final String TEST_MESSAGE_FIFO = "Hello Fifo";

    @Autowired(required = false)
    private LocalStackMessagingService localStackMessagingService;

    @Autowired
    private SqsConsumer sqsConsumer;

    @Autowired
    private SnsClient snsClient;

    @Autowired
    private S3Client s3Client;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-topic.arn}")
    private String helloTopicArn;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-topic.fifo.arn}")
    private String helloTopicFifoArn;

    @Autowired
    private TopicTagsResolver topicTagsResolver;

    @Test
    @DirtiesContext
    void testTopicAndSubscriptionsExist() {
        assertThat(localStackMessagingService).isNotNull();
        assertThat(helloTopicArn).startsWith("arn:aws:sns:eu-central-1:000000000000:").endsWith("-hello-topic");
        assertThat(helloTopicFifoArn).startsWith("arn:aws:sns:eu-central-1:000000000000:").endsWith("-hello-topic.fifo");

        assertThat(snsClient.getTopicAttributes(b -> b.topicArn(helloTopicArn)).attributes()).doesNotContainKey("FifoTopic");
        assertThat(snsClient.getTopicAttributes(b -> b.topicArn(helloTopicFifoArn)).attributes())
                .containsEntry("FifoTopic", "true");

        snsClient.listSubscriptions();

        snsClient.publish(b -> b.topicArn(helloTopicArn).message(TEST_MESSAGE));
        await().atMost(Duration.ofSeconds(10)).until(() -> sqsConsumer.getPayloads().size() == 1);
        assertThat(sqsConsumer.getPayloads()).containsExactly(TEST_MESSAGE);

        snsClient.publish(b -> b.topicArn(helloTopicFifoArn).messageGroupId(UUID.randomUUID().toString()).messageDeduplicationId(UUID.randomUUID().toString()).message(TEST_MESSAGE_FIFO));
        await().atMost(Duration.ofSeconds(10)).until(() -> sqsConsumer.getFifoPayloads().size() == 1);
        assertThat(sqsConsumer.getFifoPayloads()).containsExactly(TEST_MESSAGE_FIFO);
    }

    @Test
    void testTopicTagsResolver() {
        final Tag tag = Tag.builder().key("key1").value("value1").build();
        assertThat(topicTagsResolver.resolveTopicTags(helloTopicArn)).isEmpty();
        snsClient.tagResource(b -> b.resourceArn(helloTopicArn).tags(tag));
        snsClient.tagResource(b -> b.resourceArn(helloTopicFifoArn).tags(tag));
        assertThat(topicTagsResolver.resolveTopicTags(helloTopicArn)).isEmpty();
        assertThat(topicTagsResolver.resolveTopicTags(helloTopicFifoArn)).containsExactly(tag);
    }

    @Test
    void testCloudEventBucket() {
        assertThat(s3Client.listBuckets().buckets().stream()
                .filter(b -> b.name().endsWith(CLOUDEVENTS_BUCKET_NAME))
                .findFirst())
                .isPresent();
    }
}
