package com.tui.destilink.framework.aws.test.localstack.tests.opensearch;

import com.tui.destilink.framework.test.support.core.annotation.TestClassId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Dynamic;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@FieldNameConstants
@AllArgsConstructor
@Data
@Document(indexName = "#{@environment.getProperty('" + TestClassId.TEST_CLASS_ID_PROPERTY + "')}-test-index", dynamic = Dynamic.STRICT)
public class TestDocument {

    @Id
    @Field(type = FieldType.Keyword)
    private String documentId;
}
