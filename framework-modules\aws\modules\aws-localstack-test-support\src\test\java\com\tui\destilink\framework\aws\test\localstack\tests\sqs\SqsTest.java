package com.tui.destilink.framework.aws.test.localstack.tests.sqs;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.TextNode;
import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.SqsQueue;
import com.tui.destilink.framework.aws.test.localstack.services.LocalStackMessagingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletionException;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.MESSAGING_PROPERTY_PREFIX;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles({"it", "kubernetes"})
@LocalStack(
        sqsQueues = {
                @SqsQueue(name = "hello-world-no-fifo"),
                @SqsQueue(name = "hello-world", createDlq = true, maxReceiveCount = 1),
                @SqsQueue(name = "hello-world.fifo", createDlq = true, maxReceiveCountFifo = 10)
        }
)
class SqsTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SqsAsyncClient sqsClient;

    @Autowired(required = false)
    private LocalStackMessagingService localStackMessagingService;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.name:#{null}}")
    private String helloWorldQueueName;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.arn:#{null}}")
    private String helloWorldQueueArn;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.url:#{null}}")
    private String helloWorldQueueUrl;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.name:#{null}}")
    private String helloWorldDlqQueueName;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.arn:#{null}}")
    private String helloWorldDlqQueueArn;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.url:#{null}}")
    private String helloWorldDlqQueueUrl;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world.fifo.url:#{null}}")
    private String helloWorldFifoQueueUrl;

    @Value("${" + MESSAGING_PROPERTY_PREFIX + "hello-world-dlq.fifo.url:#{null}}")
    private String helloWorldFifoDlqQueueUrl;

    @Value("${spring.cloud.aws.endpoint}")
    private String localstackEndpoint;

    @Autowired
    private Environment env;

    @Test
    void testQueuesExists() {
        assertThat(localStackMessagingService).isNotNull();
        // No FIFO
        assertThat(env.getProperty(MESSAGING_PROPERTY_PREFIX + "hello-world-no-fifo.name")).isNotNull().endsWith("hello-world-no-fifo");
        assertThat(env.getProperty(MESSAGING_PROPERTY_PREFIX + "hello-world-no-fifo-dlq.name")).isNull();
        // Hello World
        assertThat(helloWorldQueueName).isNotNull().endsWith("-hello-world");
        assertThat(helloWorldQueueArn).isNotNull().startsWith("arn:aws:sqs:eu-central-1:000000000000:").endsWith("-hello-world");
        assertThat(helloWorldQueueUrl).isNotNull().startsWith(localstackEndpoint + "/queue/eu-central-1/000000000000/").endsWith("-hello-world");
        // Hello World DLQ
        assertThat(helloWorldDlqQueueName).isNotNull().endsWith("-hello-world-dlq");
        assertThat(helloWorldDlqQueueArn).isNotNull().startsWith("arn:aws:sqs:eu-central-1:000000000000:").endsWith("-hello-world-dlq");
        assertThat(helloWorldDlqQueueUrl).isNotNull().startsWith(localstackEndpoint + "/queue/eu-central-1/000000000000/").endsWith("-hello-world-dlq");
        // FFIFO
        assertThat(helloWorldFifoQueueUrl).isNotNull().startsWith(localstackEndpoint + "/queue/eu-central-1/000000000000/").endsWith("-hello-world.fifo");
        // FIFO DLQ
        assertThat(helloWorldFifoDlqQueueUrl).isNotNull().startsWith(localstackEndpoint + "/queue/eu-central-1/000000000000/").endsWith("-hello-world-dlq.fifo");

        assertDoesNotThrow(() -> sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join());
        assertDoesNotThrow(() -> sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldFifoQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join());

        assertThat(sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldFifoQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join().attributes())
                .containsEntry(QueueAttributeName.FIFO_QUEUE, Boolean.TRUE.toString());
        assertThat(sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldFifoDlqQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join().attributes())
                .containsEntry(QueueAttributeName.FIFO_QUEUE, Boolean.TRUE.toString());
    }

    @Test
    @DirtiesContext
    void testQueuesDestroyed() {
        localStackMessagingService.destroy();

        Exception nonFifoEx = assertThrows/* NOSONAR */(CompletionException.class, () -> sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join());
        Exception nonFifoDlqEx = assertThrows/* NOSONAR */(CompletionException.class, () -> sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldDlqQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join());
        Exception fifoEx = assertThrows/* NOSONAR */(CompletionException.class, () -> sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldFifoQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join());
        Exception fifoDlqEx = assertThrows/* NOSONAR */(CompletionException.class, () -> sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldFifoDlqQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join());


        assertInstanceOf(QueueDoesNotExistException.class, nonFifoEx.getCause());
        assertInstanceOf(QueueDoesNotExistException.class, nonFifoDlqEx.getCause());
        assertInstanceOf(QueueDoesNotExistException.class, fifoEx.getCause());
        assertInstanceOf(QueueDoesNotExistException.class, fifoDlqEx.getCause());
    }

    @Test
    void testDlqRedrivePolicy() throws Exception {
        GetQueueAttributesResponse res = sqsClient.getQueueAttributes(b -> b.queueUrl(helloWorldQueueUrl).attributeNames(List.of(QueueAttributeName.ALL))).join();
        assertThat(res.attributes()).isNotEmpty();
        assertThat(res.attributes().get(QueueAttributeName.REDRIVE_POLICY)).isNotNull();
        JsonNode policy = objectMapper.readTree(res.attributes().get(QueueAttributeName.REDRIVE_POLICY));
        assertThat(policy.get("maxReceiveCount"))
                .isInstanceOf(TextNode.class)
                .matches(n -> ((TextNode) n).textValue().equals("1"));
        assertThat(policy.get("deadLetterTargetArn"))
                .isInstanceOf(TextNode.class)
                .matches(n -> ((TextNode) n).textValue().equals(helloWorldDlqQueueArn));
    }

    @Test
    @DirtiesContext
    void testDlqRedrive() {
        final String uuid = UUID.randomUUID().toString();
        //
        sqsClient.setQueueAttributes(b -> b.queueUrl(helloWorldQueueUrl).attributes(Map.of(QueueAttributeName.VISIBILITY_TIMEOUT, "1")));
        sqsClient.setQueueAttributes(b -> b.queueUrl(helloWorldFifoQueueUrl).attributes(Map.of(QueueAttributeName.VISIBILITY_TIMEOUT, "1")));
        // SEND BOTH
        // Queue
        sqsClient.sendMessage(SendMessageRequest.builder()
                .queueUrl(helloWorldQueueUrl)
                .messageBody("Hello DLQ - " + uuid)
                .build()).join();
        // FIFO Queue
        sqsClient.sendMessage(SendMessageRequest.builder()
                .queueUrl(helloWorldFifoQueueUrl)
                .messageGroupId("test-group-" + uuid)
                .messageDeduplicationId(uuid)
                .messageBody("Hello FIFO DLQ - " + uuid)
                .build()).join();
        // RECEIVE BOTH
        // Queue
        ReceiveMessageResponse rcvrResp = receive(helloWorldQueueUrl);
        assertThat(rcvrResp.messages()).hasSize(1);
        // 2 Receives are required
        rcvrResp = receive(helloWorldQueueUrl);
        assertThat(rcvrResp.messages()).isEmpty();
        // FIFO Queue
        for (int i = 0; i < 10; i++) {
            rcvrResp = receive(helloWorldFifoQueueUrl);
            assertThat(rcvrResp.messages()).hasSize(1);
        }
        // 2 Receives are required
        rcvrResp = receive(helloWorldFifoQueueUrl);
        assertThat(rcvrResp.messages()).isEmpty();

        // CHECK BOTH DLQ
        // Queue
        rcvrResp = receive(helloWorldDlqQueueUrl);
        assertThat(rcvrResp.messages()).hasSize(1);
        assertThat(rcvrResp.messages().getFirst().body()).isEqualTo("Hello DLQ - " + uuid);
        // FIFO Queue
        rcvrResp = receive(helloWorldFifoDlqQueueUrl);
        assertThat(rcvrResp.messages()).hasSize(1);
        assertThat(rcvrResp.messages().getFirst().body()).isEqualTo("Hello FIFO DLQ - " + uuid);
    }

    private ReceiveMessageResponse receive(String queueUrl) {
        return sqsClient.receiveMessage(ReceiveMessageRequest.builder()
                .queueUrl(queueUrl)
                .maxNumberOfMessages(1)
                .visibilityTimeout(0)
                .waitTimeSeconds(10)
                .build()).join();
    }
}
