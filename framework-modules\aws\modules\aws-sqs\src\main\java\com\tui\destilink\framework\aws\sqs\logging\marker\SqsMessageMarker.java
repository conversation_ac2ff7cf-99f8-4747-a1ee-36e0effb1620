package com.tui.destilink.framework.aws.sqs.logging.marker;

import com.tui.destilink.framework.aws.sqs.util.SqsMessageConversionContextUtils;
import io.awspring.cloud.sqs.support.converter.SqsMessageConversionContext;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.UtilityClass;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@UtilityClass
public class SqsMessageMarker {

    private static final String TRUNCATED_SUFFIX = "...(truncated)";
    private static final int HEADER_MAX_LENGTH = 500 - TRUNCATED_SUFFIX.length();
    private static final String FIELD_NAME = "aws.sqs";

    private static final Set<MessageSystemAttributeName> SYSTEM_ATTRIBUTE_WHITELIST = Set.of(
            MessageSystemAttributeName.SENDER_ID,
            MessageSystemAttributeName.SENT_TIMESTAMP,
            MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT,
            MessageSystemAttributeName.APPROXIMATE_FIRST_RECEIVE_TIMESTAMP,
            MessageSystemAttributeName.MESSAGE_GROUP_ID,
            MessageSystemAttributeName.MESSAGE_DEDUPLICATION_ID
    );

    public static LogstashMarker sqsMessageMarker(Message message) {
        return sqsMessageMarker(message, null, false);
    }

    public static LogstashMarker sqsMessageMarker(Message message, SqsMessageConversionContext context) {
        return sqsMessageMarker(message, context, false);
    }

    public static LogstashMarker sqsMessageMarker(Message message, SqsMessageConversionContext context, boolean skipMessageBody) {
        return sqsMessageMarker(SqsMessageMarkerData.of(message, context, skipMessageBody));
    }

    public static LogstashMarker sqsMessageMarker(SqsMessageMarkerData messageMarker) {
        return Markers.append(FIELD_NAME, messageMarker);
    }

    @Data
    @Builder
    public static class SqsMessageMarkerData {
        private final String queueUrl;
        private final String messageId;
        private final String messageBody;
        private final Map<String, String> attributes;
        private final Map<String, Object> messageAttributes;

        public static SqsMessageMarkerData of(Message message, SqsMessageConversionContext context, boolean skipMessageBody) {
            Map<String, String> attributes = new HashMap<>();
            message.attributes().forEach((k, v) -> {
                if (SYSTEM_ATTRIBUTE_WHITELIST.contains(k)) {
                    attributes.put(k.toString(), v);
                }
            });
            return builder()
                    .queueUrl(SqsMessageConversionContextUtils.extractQueueUrl(context))
                    .messageId(message.messageId())
                    .messageBody(skipMessageBody ? "[REDACTED]" : message.body())
                    .attributes(attributes)
                    .messageAttributes(convertMessageAttributes(message.messageAttributes()))
                    .build();

        }

        private static Map<String, Object> convertMessageAttributes(Map<String, MessageAttributeValue> messageAttributes) {
            Map<String, Object> result = new HashMap<>();
            if (messageAttributes != null) {
                messageAttributes.forEach((k, v) -> result.put(k, truncate(v.toString())));
            }
            return result;
        }

        private static String truncate(String str) {
            if (str.length() > HEADER_MAX_LENGTH) {
                return str.substring(0, HEADER_MAX_LENGTH) + TRUNCATED_SUFFIX;
            }
            return str;
        }
    }
}
