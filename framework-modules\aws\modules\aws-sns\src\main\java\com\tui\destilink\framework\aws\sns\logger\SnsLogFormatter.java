package com.tui.destilink.framework.aws.sns.logger;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties;
import com.tui.destilink.framework.jackson.core.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.services.sns.model.PublishRequest;

import java.util.Iterator;
import java.util.Map;
import java.util.Optional;

@RequiredArgsConstructor
public class SnsLogFormatter {

    private final DefaultSnsContainerProperties.SnsLogging snsLogging;
    private final ObjectMapper objectMapper;

    public String format(String messageId, Map<String, String> headers, PublishRequest request) {
        final String message = request.message();
        final StringBuilder result = new StringBuilder(message.length() + 2048);
        result.append("SNS Message to Topic");
        result.append(" ");
        result.append(request.topicArn());
        result.append('\n');
        result.append('\n');
        result.append("MessageId:");
        result.append(" ");
        result.append(messageId);
        Optional.ofNullable(request.messageGroupId()).filter(StringUtils::isNotEmpty).ifPresent(messageGroupId -> {
            result.append('\n');
            result.append("MessageGroupId:");
            result.append(" ");
            result.append(messageGroupId);
        });
        Optional.ofNullable(request.messageDeduplicationId()).filter(StringUtils::isNotEmpty).ifPresent(messageDeduplicationId -> {
            result.append('\n');
            result.append("MessageDeduplicationId:");
            result.append(" ");
            result.append(messageDeduplicationId);
        });
        result.append('\n');
        result.append('\n');
        result.append("Headers: ");
        this.writeHeaders(headers, result);
        this.writeBody(message, result);
        return result.toString();
    }

    private void writeHeaders(final Map<String, String> headers, final StringBuilder output) {
        if (!headers.isEmpty()) {
            for (Iterator<Map.Entry<String, String>> var3 = headers.entrySet().iterator(); var3.hasNext(); ) {
                output.append('\n');
                Map.Entry<String, String> entry = var3.next();
                output.append(entry.getKey());
                output.append(": ");
                output.append(entry.getValue());
            }
        }
    }

    private void writeBody(final String body, final StringBuilder output) {
        if (!body.isEmpty()) {
            output.append('\n');
            output.append('\n');
            output.append("Message: ");
            if (Boolean.TRUE.equals(snsLogging.getPrettyPrint()))
                output.append(JsonUtils.prettyPrintRawJsonIfPossible(objectMapper, body));
            else
                output.append(body);
        }
    }
}