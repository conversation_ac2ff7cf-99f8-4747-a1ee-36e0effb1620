package com.tui.destilink.framework.aws.test.localstack.tests.opensearch;

import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.OpenSearchDomain;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.opensearch.data.client.orhlc.NativeSearchQuery;
import org.opensearch.data.client.orhlc.NativeSearchQueryBuilder;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.spring.boot.autoconfigure.OpenSearchProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles({"opensearch-it"})
@LocalStack(openSearchDomain = @OpenSearchDomain)
class OpenSearchIT {

    @Autowired
    private ElasticsearchOperations openSearchRestTemplate;

    @Autowired
    private TestDocumentRepository testDocumentRepository;

    @Autowired
    private OpenSearchProperties properties;

    /**
     * https://docs.localstack.cloud/user-guide/aws/opensearch/
     * <p>
     * 1. Local Tests will use PATH Endpoints like this:
     * localhost:4566/opensearch/eu-central-1/a-9590405d-test-domain
     * <p>
     * but opensearch cant use them as is. Therefor we need to split them into 'uris' and 'pathPrefix'
     * uris: localhost:4566
     * pathPrefix: /opensearch/eu-central-1/a-9590405d-test-domain
     * <p>
     * <p>
     * 2. Pipeline Tests will use DOMAIN Endpoints like this:
     * a-9590405d-test-domain.eu-central-1.opensearch.localstack:4566
     * <p>
     * opensearch can use them as is for 'uris' and doesn't need a pathPrefix
     */

    @Test
    void testIndexWorkAsExpected() {
        assertThat(properties.getUris()).hasSize(1);
        assertThat(properties.getUris().getFirst()).isNotNull().contains(":4566");
        if (StringUtils.isBlank(properties.getPathPrefix()))
            assertThat(properties.getUris().getFirst()).isNotNull().contains("default");
        else
            assertThat(properties.getPathPrefix()).isNotNull().contains("default");

        testDocumentRepository.save(new TestDocument("123"));
        testDocumentRepository.save(new TestDocument("321"));
        assertThat(testDocumentRepository.findById("123")).isNotNull();

        BoolQueryBuilder boolQuery = new BoolQueryBuilder().must(QueryBuilders.termQuery(TestDocument.Fields.documentId, "321"));
        NativeSearchQuery nativeQuery = new NativeSearchQueryBuilder().withQuery(boolQuery).build();
        assertThat(openSearchRestTemplate.search(nativeQuery, TestDocument.class).getSearchHits()).hasSize(1);
    }
}