package com.tui.destilink.framework.aws.sns.logger;

import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties;
import com.tui.destilink.framework.aws.sns.util.MessageAttributeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Marker;
import software.amazon.awssdk.services.sns.model.PublishRequest;

import java.util.Map;

@Slf4j
@AllArgsConstructor
public class SnsLogWriter {

    private final DefaultSnsContainerProperties.SnsLogging snsLogging;
    private final SnsLogFormatter snsLogFormatter;

    public void write(String messageId, PublishRequest request) {
        Map<String, String> header = MessageAttributeUtils.toHeaderMap(request.messageAttributes());

        Marker context = getContext(messageId, request, header);
        if (Boolean.TRUE.equals(snsLogging.getEnabled())) {
            log.info(context, "{}", snsLogFormatter.format(messageId, header, request));
        } else {
            log.info(context, "SNS Message to Topic {} with {}", request.topicArn(), messageId);
        }
    }

    private static Marker getContext(String messageId, PublishRequest request, Map<String, String> header) {
        return Markers.append("aws.sns.topic.arn", request.topicArn())
                .and(addToMarker("aws.sns.messageId", messageId))
                .and(addToMarker("aws.sns.messageGroupId", request.messageGroupId()))
                .and(addToMarker("aws.sns.messageDeduplicationId", request.messageDeduplicationId()))
                .and(addToMarker("aws.sns.headers", header));
    }

    private static Marker addToMarker(String key, Map<String, String> header) {
        return header == null ? Markers.empty() : Markers.append(key, header);
    }

    private static Marker addToMarker(String key, String value) {
        return StringUtils.isBlank(value) ? Markers.empty() : Markers.append(key, value);
    }
}