# Redis Locking Module: Architecture Overview

## 1. Introduction

This document outlines the architecture for the `locking-redis-lock` module within the Destilink Framework. It provides a robust, performant, and developer-friendly distributed locking mechanism using Redis. The architecture emphasizes shared, Spring-managed components, efficient non-polling lock acquisition, clear configuration hierarchies, and adherence to overall Destilink Framework modernization guidelines.

## 2. Core Architectural Principles

*   **Spring-Managed Shared Components**: Critical, stateless, or resource-intensive components (`ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `LockMonitor`, `LockComponentRegistry`) are implemented as Spring-managed singleton beans, configured explicitly within `RedisLockAutoConfiguration`.
*   **Centralized Component Access**: A `LockComponentRegistry` bean acts as a central holder for these shared services, simplifying dependency injection into lock implementations.
*   **Efficient Non-Polling Lock Acquisition**: Lock acquisition primarily relies on Redis Pub/Sub for unlock notifications, managed via `UnlockMessageListener` (one per bucket, per `lockName`) and `LockSemaphoreHolder`, minimizing direct polling.
*   **Contextual and Structured Exception Handling**: A defined exception hierarchy, rooted in `AbstractRedisLockException` (which implements `ExceptionMarkerProvider`), provides detailed context for improved diagnostics and structured logging.
*   **Explicit Configuration & Dependency Injection**: Strict adherence to Destilink Framework guidelines for auto-configuration:
    *   `@AutoConfiguration` for the module's entry point (`RedisLockAutoConfiguration`).
    *   Explicit `@Bean` definitions for all managed components.
    *   No use of `@ComponentScan` within the module.
    *   Constructor injection is preferred.
    *   Registration in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.
*   **Clear Configuration Hierarchy**: Global YAML settings provide base defaults. Programmatic builders for buckets and lock instances allow overriding specific, permissible properties. (Detailed in [Configuration](configuration.md)).
*   **Lightweight Lock Instances**: Concrete lock implementations (e.g., `RedisReentrantLock`) are lean, delegating shared responsibilities to the Spring-managed beans via the `LockComponentRegistry`.
*   **Enhanced Logging**: Comprehensive logging strategy using SLF4J, MDC via `LockContextDecorator`, and structured exception details via `ExceptionMarkerProvider`.

## 3. Component Diagram

```mermaid
graph TD
    subgraph ApplicationCode ["Application Code"]
        AppLockUser["Service/User of Lock"];
    end

    subgraph LockingModuleFacade ["Locking Module Facade (Builders & Registry)"]
        LBR["`LockBucketRegistry` (Bean)"];
        LBB["`LockBucketBuilder`"];
        LCB["`LockConfigBuilder`"];
        ALTCB["`AbstractLockTypeConfigBuilder` & Subclasses"];
    end

    subgraph CoreLockingLogic ["Core Locking Logic"]
        direction LR;
        ARL["`AbstractRedisLock` (Base Class)"];
        RLI["Concrete Lock Implementations<br>(e.g., `RedisReentrantLock`)"];
        LSH["`LockSemaphoreHolder` (Per Lock Key, Guava Cache Managed)"];
    end
    
    subgraph SharedSpringServices ["Shared Spring-Managed Services"]
        direction LR;
        LCR["`LockComponentRegistry` (Bean)"];
        SL["`ScriptLoader` (Bean)"];
        UMLM["`UnlockMessageListenerManager` (Bean)"];
        UML["`UnlockMessageListener` (Per Bucket & Lock Key, Managed by UMLM)"];
        LW["`LockWatchdog` (Bean)"];
        ROps["`RedisLockOperations` (Bean)"];
        LMP["`LockMonitor` (Bean, Optional)"];
        LOS["`DefaultLockOwnerSupplier` (Bean)"];
        LERRH["`RedisLockErrorHandler` (Bean)"];
    end

    subgraph Configuration ["Configuration & AutoConfiguration"]
        direction LR;
        RLP["`RedisLockProperties` (Global YAML-backed)"];
        LBC["`LockBucketConfig` (Programmatically Resolved for Bucket)"];
        RAutoConfig["`RedisLockAutoConfiguration` (@AutoConfiguration)"];
    end

    subgraph Exceptions ["Exception Handling"]
        BaseExc["`AbstractRedisLockException` (implements ExceptionMarkerProvider)"];
        SpecExc["Specialized Lock Exceptions..."];
    end

    subgraph Logging ["Logging Infrastructure"]
        SLF4J["SLF4J API"];
        MDC["MDC (via LockContextDecorator)"];
        EMP["ExceptionMarkerProvider (via Exceptions)"];
        CoreLogging["Destilink Core Logging (ExceptionLogstashMarkersJsonProvider)"];
    end

    subgraph ExternalSystems ["External Systems"]
        Redis["Redis (Cluster)"];
    end

    AppLockUser --> LBR;
    LBR -- "creates" --> LBB;
    LBB --> LCB;
    LCB --> ALTCB;
    ALTCB -- ".build() creates" --> RLI;
    RLI -- "extends" --> ARL;
    
    ARL -- "uses services from" --> LCR;
    ARL -- "uses for waiting" --> LSH;
    ARL -- "throws" --> Exceptions;
    ARL -- "logs via" --> SLF4J;

    LCR -- "provides" --> SL;
    LCR -- "provides" --> UMLM;
    LCR -- "provides" --> LW;
    LCR -- "provides" --> ROps;
    LCR -- "provides (optional)" --> LMP;
    LCR -- "provides" --> LOS;
    LCR -- "provides" --> LERRH;

    UMLM -- "manages & provides" --> UML;
    UML -- "manages `LockSemaphoreHolder` map via Guava Cache" --> LSH;
    UML -- "listens to Pub/Sub from" --> Redis; # Channel is <prefix>:<bucketName>:__unlock_channels__:{{lockName}}
    UML -- "signals" --> LSH;
    
    RAutoConfig -- "defines bean" --> LBR;
    RAutoConfig -- "defines bean" --> LCR;
    RAutoConfig -- "defines bean" --> SL;
    RAutoConfig -- "defines bean" --> UMLM;
    RAutoConfig -- "defines bean" --> LW;
    RAutoConfig -- "defines bean" --> ROps;
    RAutoConfig -- "defines bean (conditional)" --> LMP;
    RAutoConfig -- "defines bean" --> LOS;
    RAutoConfig -- "defines bean" --> LERRH;
    RAutoConfig -- "enables" --> RLP;

    RLP -- "provides global defaults for" --> LBC;
    LBR -- "initializes & resolves config into" --> LBC;
    LBC -- "provides config to" --> LBB; # LBB can further refine bucket-level defaults

    ROps -- "uses" --> SL;
    ROps -- "interacts with" --> Redis;
    ROps -- "uses" --> LERRH;
    LW -- "extends lease in" --> Redis;
    
    Exceptions -- "are subclasses of" --> BaseExc;
    BaseExc -- "provides marker to" --> EMP;
    EMP -- "consumed by" --> CoreLogging;
    SLF4J -- "writes to" --> MDC;
    SLF4J -- "integrates with" --> CoreLogging;


    style AppLockUser fill:#lightgrey;
    style LBR fill:#lightblue;
    style LBB fill:#lightblue;
    style LCB fill:#lightblue;
    style ALTCB fill:#lightblue;
    style ARL fill:#adebad;
    style RLI fill:#adebad;
    style LSH fill:#adebad;
    style LCR fill:#ccffcc;
    style SL fill:#ffffcc;
    style UMLM fill:#ffffcc;
    style UML fill:#ffffcc;
    style LW fill:#ffffcc;
    style ROps fill:#ffffcc;
    style LMP fill:#ffffcc;
    style LOS fill:#ffffcc;
    style LERRH fill:#ffffcc;
    style RLP fill:#ffcc99;
    style LBC fill:#fdd;
    style RAutoConfig fill:#ffcc99;
    style Exceptions fill:#ffdddd;
    style Logging fill:#e6e6fa;
    style Redis fill:#ffcccc;
```

## 4. Key Components and Responsibilities

### 4.1. Configuration Components
*   **`RedisLockProperties` (Java Class)**:
    *   Maps global configurations from YAML files (`destilink.fw.locking.redis.*`).
    *   Provides default values for core settings like `leaseTime`, `retryInterval`, `watchdogMaxTtl`, `watchdogRenewalMultiplier`, `stateKeyExpiration`, etc.
    *   Annotated with `@ConfigurationProperties(prefix = "destilink.fw.locking.redis")`.
    *   **Does not** contain a map for YAML-based bucket definitions. Bucket configurations are now primarily programmatic via builders, starting with global defaults.
*   **`LockBucketConfig` (Java Class)**:
    *   Holds the *resolved* and *effective* configuration for a specific lock bucket.
    *   It's initialized with global defaults from `RedisLockProperties` by the `LockBucketRegistry`.
    *   Certain bucket-level defaults (like `useWatchdog`, default `leaseTime` for the bucket) can be overridden programmatically via `LockBucketBuilder`.
    *   Some properties (e.g., `useWatchdog` policy, scope) become final for all locks created within that bucket configuration context once set by the builder.
*   **`RedisLockAutoConfiguration` (Java Class)**:
    *   The module's Spring Boot `@AutoConfiguration` entry point.
    *   **Strictly adheres to no `@ComponentScan`**. All framework beans are explicitly defined via `@Bean` methods or imported via `@Import`.
    *   Conditionally enables the locking module based on the `destilink.fw.locking.redis.enabled` property.
    *   Responsible for instantiating and wiring all shared service beans (see below) and the `LockBucketRegistry`.
    *   Must be registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.

### 4.2. Shared Spring-Managed Services
These services are instantiated once by Spring (via `RedisLockAutoConfiguration`) and made available to lock instances through the `LockComponentRegistry`.

*   **`LockComponentRegistry` (Bean)**:
    *   A central registry injected into lock builders.
    *   Provides access to all other shared services (`ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, etc.), simplifying dependency management for lock instances.
*   **`ScriptLoader` (Bean)**:
    *   Loads all necessary Lua scripts from the classpath during application startup (`@PostConstruct`).
    *   Caches scripts for efficient reuse by `RedisLockOperations`.
*   **`UnlockMessageListenerManager` (Bean)**:
    *   Manages the lifecycle of `UnlockMessageListener` instances.
    *   Creates and maintains **one `UnlockMessageListener` instance per unique lock key (`<prefix>:<bucketName>:__unlock_channels__:{{lockName}}`)** upon first request for notification on that specific lock key. This means listeners are more granular, directly tied to the resource being locked.
    *   Each `UnlockMessageListener` will internally manage a `LockSemaphoreHolder` instance (not a sharded map, as the listener itself is now per lock key). This `LockSemaphoreHolder` is likely managed with Guava `CacheBuilder` using `weakValues()` to ensure it's GC-eligible when no threads are waiting on it.
*   **`UnlockMessageListener` (Per Lock Key, Managed by Manager)**:
    *   Subscribes to a Redis Pub/Sub channel specific to its `lockKey` (e.g., `myApp:orders:__unlock_channels__:{order123}`) to receive unlock notifications.
    *   On receiving a message, signals its `LockSemaphoreHolder`.
    *   Uses an optimal executor (virtual threads if available) for asynchronous notification processing.
*   **`LockWatchdog` (Bean)**:
    *   Periodically extends the TTL of active, application-instance-bound locks.
    *   Maintains a registry of monitored locks.
    *   Uses a Lua script (via `RedisLockOperations`) to atomically verify ownership and extend the lock's TTL in Redis. Renewal frequency is based on `watchdogMaxTtl` and `watchdogRenewalMultiplier`.
*   **`RedisLockOperations` (Bean, Implemented by `RedisLockOperationsImpl`)**:
    *   Provides an abstraction layer for Redis communication specific to lock operations.
    *   Executes Lua scripts (obtained from `ScriptLoader`) for acquiring, releasing, and extending locks.
    *   Handles idempotency caching for script executions.
*   **`DefaultLockOwnerSupplier` (Bean)**:
    *   Provides the default mechanism for generating unique owner IDs for lock instances (e.g., combining application instance ID and thread ID). Application developers can provide a custom implementation.
*   **`RedisLockErrorHandler` (Bean)**:
    *   Centralizes logic for interpreting exceptions from Redis operations and translating them into specific `AbstractRedisLockException` subtypes.
*   **`LockMonitor` (Bean, Optional)**:
    *   Collects and exposes metrics related to lock operations using Micrometer (e.g., acquisition times, contention rates). Conditionally activated.

### 4.3. Core Locking Logic & Instantiation
*   **`LockBucketRegistry` (Bean)**:
    *   The primary factory for creating lock instances.
    *   Initiates the fluent builder API (`builder(String bucketName)` or `builder(String bucketName, String applicationInstanceId)`).
    *   Initializes a `LockBucketConfig` for the specified bucket using global defaults from `RedisLockProperties`.
    *   Injects shared dependencies (via `LockComponentRegistry`) and the initialized `LockBucketConfig` into the `LockBucketBuilder`.
*   **`LockBucketBuilder`**:
    *   First stage of the fluent builder API.
    *   Configures bucket-level concerns: lock scope (application vs. distributed), custom `LockOwnerSupplier`, and default values for `leaseTime`, `retryInterval`, `useWatchdog`, `stateKeyExpiration` for this bucket context. These modify the passed-in `LockBucketConfig`.
    *   Transitions to `LockConfigBuilder`.
*   **`LockConfigBuilder`**:
    *   Allows selection of the lock type (e.g., reentrant, read-write, state, stamped).
    *   Transitions to a type-specific builder (e.g., `ReentrantLockConfigBuilder`).
*   **`AbstractLockTypeConfigBuilder` and Subclasses (e.g., `ReentrantLockConfigBuilder`)**:
    *   Allow overriding instance-specific parameters (e.g., `leaseTime`, `retryInterval`, type-specific parameters like `expectedState` for `RedisStateLock`).
    *   The `.build()` method constructs the concrete lock instance using the fully resolved configuration.
*   **`AbstractRedisLock` (Base Class)**:
    *   Provides common functionality for all Redis lock implementations.
    *   Manages the core lock acquisition and release lifecycle, including reentrancy logic (if applicable to the subclass).
    *   Implements the non-polling wait mechanism using `UnlockMessageListener` (obtained via `UnlockMessageListenerManager` from `LockComponentRegistry` for the specific lock key) and `LockSemaphoreHolder`.
    *   Delegates Redis script execution to `RedisLockOperations`.
    *   Registers/unregisters with `LockWatchdog`.
*   **Concrete Lock Implementations (e.g., `RedisReentrantLock`, `RedisReadWriteLock`, `RedisStateLock`, `RedisStampedLock`)**:
    *   Extend `AbstractRedisLock`.
    *   Implement abstract methods to provide the specific Lua script names and arguments for their particular locking semantics (e.g., `tryAcquireLockScript`, `releaseLockScript`).
*   **`LockSemaphoreHolder`**:
    *   A non-Spring managed helper class, instantiated and managed by `UnlockMessageListener` (likely via Guava Cache with `weakValues()`) per `lockKey`.
    *   Encapsulates a `java.util.concurrent.Semaphore` and an `AtomicInteger` for `waiters`.
    *   Used by `AbstractRedisLock` to wait for unlock notifications. Drains stale permits before waiting. The wait time is determined by the lock's current TTL in Redis, not a separate notification timeout property.

### 4.4. Exception Handling
*   A structured hierarchy of exceptions extends `AbstractRedisLockException`.
*   `AbstractRedisLockException` implements [`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/ExceptionMarkerProvider.java:1) to provide detailed contextual information for structured logging via SLF4J Markers.
*   Includes specific exceptions like `LockTimeoutException`, `LockAcquisitionException`, `LockReleaseException`, `LockNotOwnedException`, `LeaseExtensionException`, and `LockCommandException`.
*   These exceptions carry contextual information such as `lockKey`, `lockType`, and relevant IDs or timeouts, which are exposed through the `getMarker()` method. (See [Exception Handling](exception_handling.md) for details).

## 5. Redis Key Schema

The module employs a structured approach to naming keys in Redis, as primarily defined by the key construction logic within [`LockBucket.java`](../src/main/java/com/tui/destilink/framework/locking/redis/lock/LockBucket.java). This schema ensures clarity, prevents collisions, and facilitates Redis Cluster compatibility through hash tags.

*   **Key Prefix (`<prefix>`)**: Determined by the lock scope:
    *   **Application Scope**: Uses `RedisCoreProperties.KeyspacePrefixes#getApplicationPrefix()` (e.g., "myApp" if the application name is "myApp").
    *   **Distributed Scope**: Uses `RedisCoreProperties.KeyspacePrefixes#getDistributedPrefix()` (e.g., "\_\_distributed\_\_").
    *   The global key prefix (e.g., `destilink.fw.redis.default-prefix` from `redis-core`) might also be prepended by `RedisKeyPrefix` utilities.
*   **Bucket Name (`<bucketName>`)**: The logical grouping for locks, provided when creating a lock builder (e.g., "orders", "inventory").
*   **Namespace Separator**: `__locks__` is used to group all lock-specific keys for a bucket.
*   **Hash Tag (`{{lockName}}`)**: The actual `lockName` (or `lockIdentifier`) is enclosed in curly braces `{{}}`. This ensures that all Redis keys related to the same logical lock (e.g., main lock, state key, watchdog keys) reside on the same Redis Cluster node, enabling multi-key Lua scripts to operate correctly.

### 5.1. Base Lock Key

This is the fundamental key for a lock.
*   **Format**: `<prefix>:<bucketName>:__locks__:{{lockName}}`
*   **Example (Application Scope)**: `myApp:orders:__locks__:{order123}`
*   **Example (Distributed Scope)**: `__distributed__:inventory:__locks__:{productXYZ}`
*   **Data Stored**:
    *   **Simple/Non-Reentrant Lock**: The value is the `lockOwnerId`.
    *   **Reentrant Lock**: Typically a Redis Hash storing `ownerId` and `count`.
    *   **ReadWrite/Stamped Lock**: A Redis Hash storing relevant fields like owner, reader count, stamp, mode.
*   *Reasoning*: The hash tag `{{lockName}}` is critical for Redis Cluster. The `__locks__` segment provides clear namespacing.

### 5.2. Lock Type Suffixes

Specific lock types or features might append suffixes to the base lock key. These suffixes are part of the key that is *outside* the hash tag if they are for distinct but related data, or fields within a Hash if the data is part of the main lock entity. The Javadoc in `LockBucket.java` mentions suffixes like `:state`, `:read`, `:write` appended to the full base key.

*   **State Key (for `RedisStateLock`)**:
    *   **Format**: `<prefix>:<bucketName>:__locks__:{{lockName}}:state`
    *   *Reasoning*: Separates state data while keeping it related to the main lock key. The hash tag on `lockName` ensures co-location if needed for multi-key operations (though typically state checks and lock acquisitions are separate operations).
*   **Read/Write Lock Watchdog Suffixes**:
    *   Format: `<prefix>:<bucketName>:__locks__:{{lockName}}:read` or `:write`
    *   Used for storing specific watchdog information if read/write locks have distinct lease management needs not covered by the main lock key's watchdog.
*   *Reasoning*: Suffixes help differentiate data types or aspects associated with the same logical lock.

### 5.3. Idempotency Cache Keys

Used by `RedisLockOperations` to cache responses.
*   **Format**: `<prefix>:<bucketName>:__resp_cache__:{{lockName}}:<requestUuid>`
*   **Example**: `myApp:orders:__resp_cache__:{order123}:a1b2c3d4-e5f6-7890-1234-567890abcdef`
*   *Reasoning*: Ensures the idempotency key is co-located with the lock key in Redis Cluster. `__resp_cache__` provides clear namespacing.

### 5.4. Pub/Sub Channel Names (Unlock Notifications)

Used for broadcasting unlock events.
*   **Format**: `<prefix>:<bucketName>:__unlock_channels__:{{lockName}}`
*   **Example**: `myApp:orders:__unlock_channels__:{order123}`
*   **Message Payload**: JSON string containing `lockKey` (the full Redis key of the *base lock key*) and `unlockType`.
*   *Reasoning*: Channel is specific to the lock key, ensuring targeted notifications.

**Key Design Considerations for Schema (from `LockBucket.java`):**
*   **Namespacing**: Achieved via `<prefix>`, `<bucketName>`, and `__locks__`, `__resp_cache__`, `__unlock_channels__` segments.
*   **Redis Cluster Compatibility**: The `{{lockName}}` hash tag on the core lock identifier is crucial.
*   **Readability**: Keys are structured and human-readable.

## 6. Data Flow and Interactions

(Refer to detailed flow descriptions in [Lock Acquisition Flow](lock_acquisition_flow.md) and [Watchdog Mechanism](watchdog.md)).

## 7. Adherence to Destilink Framework Guidelines

This architecture is designed to comply with the Destilink Framework guidelines ([`.amazonq/rules/guidelines.md`](../../../../../.amazonq/rules/guidelines.md:1)), with a particular focus on:
*   **No `@ComponentScan`**: `RedisLockAutoConfiguration` uses explicit bean definitions.
*   **Explicit Dependencies**: Constructor injection is favored.
*   **Standard Configuration Patterns**: Use of `@ConfigurationProperties`, `@AutoConfiguration`, and standard registration.
*   **Package and Naming Conventions**: Aligned with framework standards.
*   **Structured Logging**: Integration with core logging framework.

## 8. Logging Strategy

The `locking-redis-lock` module employs a comprehensive logging strategy to ensure observability and aid in diagnostics.

*   **SLF4J API**: All logging is performed using the SLF4J API, allowing for flexible binding to underlying logging implementations (e.g., Logback).
*   **Parameterized Logging**: Messages are logged using parameterized placeholders to avoid string concatenation overhead and improve readability:
    ```java
    log.debug("Attempting to acquire lock '{}' for owner '{}'", lockName, ownerId);
    ```
*   **Mapped Diagnostic Context (MDC)**:
    *   The [`LockContextDecorator`](../src/main/java/com/tui/destilink/framework/locking/redis/logging/context/LockContextDecorator.java:1) (which should align with [`AbstractContextDecorator`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/context/decorator/AbstractContextDecorator.java:1) from the core framework) is used to enrich log messages with contextual information.
    *   It populates the MDC with key-value pairs like `lock.name`, `lock.operation` (e.g., "acquire", "release", "extend"), `lock.ownerId`, and `lock.requestId`.
    *   This context is typically applied using a try-with-resources block with the `Scope` object provided by the decorator:
        ```java
        try (Scope scope = lockContextDecorator.withContext(lockName, "tryLock", ownerId, requestUuid)) {
            log.info("Lock operation started.");
            // ... lock logic ...
            log.info("Lock operation finished.");
        }
        ```
    *   MDC data is automatically included in structured log formats (e.g., JSON by Logstash Logback Encoder).
*   **Structured Exception Logging**:
    *   As detailed in Section 4.4 and [Exception Handling](exception_handling.md), all custom lock exceptions extending `AbstractRedisLockException` implement `ExceptionMarkerProvider`.
    *   The `getMarker()` method provides a rich set of contextual key-value pairs specific to the error condition.
    *   The Destilink Core logging framework's [`ExceptionLogstashMarkersJsonProvider`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/ExceptionLogstashMarkersJsonProvider.java:1) consumes these markers, ensuring that detailed, structured exception information is included in the JSON log output. This facilitates easier parsing, searching, and alerting on specific error conditions.
*   **Log Levels**:
    *   `DEBUG`: Detailed information for step-by-step debugging of lock operations (e.g., script execution details, semaphore state changes).
    *   `INFO`: Key lifecycle events, successful lock acquisitions/releases, watchdog activity.
    *   `WARN`: Recoverable issues, potential misconfigurations, or unexpected but non-critical states (e.g., lock contention leading to retries).
    *   `ERROR`: Unrecoverable errors, failed lock operations that impact application logic. Exception stack traces are logged at this level, enriched by `ExceptionMarkerProvider`.

This modernized architecture aims to deliver a more reliable, maintainable, and efficient distributed locking solution.