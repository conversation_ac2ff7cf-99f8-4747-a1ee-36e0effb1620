package com.tui.destilink.framework.aws.sqs.resolver.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.aws.sqs.resolver.QueueAttributesResolver;
import com.tui.destilink.framework.aws.sqs.resolver.QueueDlqUrlResolver;
import com.tui.destilink.framework.aws.sqs.resolver.SqsResolveException;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public class CachingQueueDlqUrlResolver extends AbstractResolver<String> implements QueueDlqUrlResolver {

    private static final String SQS_REDRIVE_POLICY_TARGET_ARN = "deadLetterTargetArn";

    private final QueueAttributesResolver attributesResolver;
    private final ObjectMapper objectMapper;

    public CachingQueueDlqUrlResolver(SqsAsyncClient sqsAsyncClient, QueueAttributesResolver attributesResolver, ObjectMapper objectMapper) {
        super(sqsAsyncClient);
        this.attributesResolver = attributesResolver;
        this.objectMapper = objectMapper;
    }

    @Override
    public CompletableFuture<String> resolveQueueDlqUrl(String queueUrlArnName) throws SqsResolveException {
        return resolveWithCache(queueUrlArnName)
                .whenComplete(logException("Failed to resolve DLQ for SQS queue " + queueUrlArnName))
                .toCompletableFuture();
    }

    @Override
    protected CompletionStage<String> resolveFromSource(String queueUrl) {
        return attributesResolver.resolveQueueAttributes(queueUrl)
                .thenApply(attributes -> {
                    String redrivePolicy = attributes.getQueueAttribute(QueueAttributeName.REDRIVE_POLICY);
                    if (StringUtils.hasLength(redrivePolicy)) {
                        try {
                            return objectMapper.readTree(redrivePolicy)
                                    .get(SQS_REDRIVE_POLICY_TARGET_ARN).textValue();
                        } catch (Exception ex) {
                            throw new SqsResolveException("Failed to resolve DLQ Target ARN from redrive policy of SQS queue " + queueUrl, ex);
                        }
                    }
                    return null;
                })
                .thenCompose(deadLetterTargetArn -> {
                    if (deadLetterTargetArn != null) {
                        // Enrich with source queue Arn
                        return resolveQueueUrl(deadLetterTargetArn)
                                .exceptionally(ex -> {
                                    throw new SqsResolveException("Failed to resolve DLQ SQS Queue url form arn " + deadLetterTargetArn, extractCauseIfCompetition(ex));
                                });
                    }
                    return CompletableFuture.completedFuture(null);
                });
    }
}
