package com.tui.destilink.framework.aws.test.localstack.tests.util;

import com.tui.destilink.framework.aws.test.localstack.TestApplication;
import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.S3Bucket;
import com.tui.destilink.framework.aws.test.localstack.annotations.SnsTopic;
import com.tui.destilink.framework.aws.test.localstack.annotations.SqsQueue;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static com.tui.destilink.framework.aws.test.localstack.services.LocalStackMessagingService.*;
import static com.tui.destilink.framework.aws.test.localstack.tests.util.LocalStackPropertiesResolverTest.*;
import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles({"it"})
@LocalStack(
        s3Buckets = {
                @S3Bucket(name = BUCKET_NAME),
        },
        sqsQueues = {
                @SqsQueue(name = QUEUE_NAME, createDlq = true),
                @SqsQueue(name = FIFO_QUEUE_NAME, createDlq = true)
        },
        snsTopics = {
                @SnsTopic(name = TOPIC_NAME)
        }
)
class LocalStackPropertiesResolverTest {

    protected static final String BUCKET_NAME = "hello-bucket";
    protected static final String QUEUE_NAME = "hello-queue";
    protected static final String FIFO_QUEUE_NAME = "hello-queue.fifo";
    protected static final String TOPIC_NAME = "hello-topic";

    @Autowired
    private LocalStackPropertiesResolver propertiesProvider;

    @Test
    void testBucket() {
        assertThat(propertiesProvider.getBucketName(BUCKET_NAME)).endsWith(BUCKET_NAME);
    }

    @Test
    void testQueue() {
        assertThat(propertiesProvider.getQueueName(QUEUE_NAME)).endsWith(QUEUE_NAME);
        assertThat(propertiesProvider.getQueueUrl(QUEUE_NAME)).endsWith(QUEUE_NAME);
        assertThat(propertiesProvider.getQueueArn(QUEUE_NAME)).endsWith(QUEUE_NAME);
        assertThat(propertiesProvider.getQueueDlqName(QUEUE_NAME)).endsWith(QUEUE_NAME + DLQ_SUFFIX);
        assertThat(propertiesProvider.getQueueDlqUrl(QUEUE_NAME)).endsWith(QUEUE_NAME + DLQ_SUFFIX);
        assertThat(propertiesProvider.getQueueDlqArn(QUEUE_NAME)).endsWith(QUEUE_NAME + DLQ_SUFFIX);
    }

    @Test
    void testFifoQueue() {
        assertThat(propertiesProvider.getQueueName(FIFO_QUEUE_NAME)).endsWith(FIFO_QUEUE_NAME);
        assertThat(propertiesProvider.getQueueUrl(FIFO_QUEUE_NAME)).endsWith(FIFO_QUEUE_NAME);
        assertThat(propertiesProvider.getQueueArn(FIFO_QUEUE_NAME)).endsWith(FIFO_QUEUE_NAME);
        assertThat(propertiesProvider.getQueueDlqName(FIFO_QUEUE_NAME)).endsWith(FIFO_QUEUE_NAME.replace(FIFO_SUFFIX, FIFO_DLQ_SUFFIX));
        assertThat(propertiesProvider.getQueueDlqUrl(FIFO_QUEUE_NAME)).endsWith(FIFO_QUEUE_NAME.replace(FIFO_SUFFIX, FIFO_DLQ_SUFFIX));
        assertThat(propertiesProvider.getQueueDlqArn(FIFO_QUEUE_NAME)).endsWith(FIFO_QUEUE_NAME.replace(FIFO_SUFFIX, FIFO_DLQ_SUFFIX));
    }

    @Test
    void testTopic() {
        assertThat(propertiesProvider.getTopicName(TOPIC_NAME)).endsWith(TOPIC_NAME);
        assertThat(propertiesProvider.getTopicArn(TOPIC_NAME)).endsWith(TOPIC_NAME);
    }
}
