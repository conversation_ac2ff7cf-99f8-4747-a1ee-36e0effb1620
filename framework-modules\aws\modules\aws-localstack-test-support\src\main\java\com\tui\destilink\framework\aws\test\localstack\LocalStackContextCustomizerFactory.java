package com.tui.destilink.framework.aws.test.localstack;


import com.tui.destilink.framework.aws.test.localstack.annotations.*;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import org.springframework.test.context.ContextConfigurationAttributes;
import org.springframework.test.context.ContextCustomizer;
import org.springframework.test.context.ContextCustomizerFactory;
import org.springframework.test.context.TestContextAnnotationUtils;

import java.util.List;
import java.util.Set;

public class LocalStackContextCustomizerFactory implements ContextCustomizerFactory {

    @Override
    public ContextCustomizer createContextCustomizer(Class<?> testClass, List<ContextConfigurationAttributes> configAttributes) {
        LocalStack localStack = TestContextAnnotationUtils.findMergedAnnotation(testClass, LocalStack.class);
        if (localStack != null) {
            Set<S3Bucket> s3Buckets = TestContextAnnotationUtils.getMergedRepeatableAnnotations(testClass, S3Bucket.class);
            Set<SqsQueue> sqsQueues = TestContextAnnotationUtils.getMergedRepeatableAnnotations(testClass, SqsQueue.class);
            Set<SnsTopic> snsTopics = TestContextAnnotationUtils.getMergedRepeatableAnnotations(testClass, SnsTopic.class);
            Set<SnsSubscription> snsSubscriptions = TestContextAnnotationUtils.getMergedRepeatableAnnotations(testClass, SnsSubscription.class);

            return new LocalStackContextCustomizer(
                    generatePrefix(localStack, testClass),
                    localStack,
                    s3Buckets,
                    sqsQueues,
                    snsTopics,
                    snsSubscriptions
            );
        }

        return null;
    }

    private String generatePrefix(LocalStack localStack, Class<?> testClass) {
        if (localStack.useNamePrefixes()) {
            // Use has of testClass as prefix
            return TestUtils.generateTestClassId(testClass) + "-";
        }
        return null;
    }

}
