spring:
  application:
    name: destilink-ci-test-app
destilink:
  fw:
    aws:
      elasticache:
        redis:
          host: clustercfg.framework-ci-tests-valkey-cluster.iuv9e8.euc1.cache.amazonaws.com
          iam-auth:
            replication-group-id: framework-ci-tests-valkey-cluster
            user-id: svc-destilink-ci-test-app
    redis:
      core:
        monitoring:
          metrics:
            enable-pool-metrics: true
            enable-command-metrics: true
            enable-topology-metrics: true
            tag-command-types: true
          logging:
            log-pool-events: true
            log-command-execution: false
            log-topology-events: true
    caching:
      caches:
        cache1:
          type: java.lang.String
          use-compression: false
          ttl: PT10S