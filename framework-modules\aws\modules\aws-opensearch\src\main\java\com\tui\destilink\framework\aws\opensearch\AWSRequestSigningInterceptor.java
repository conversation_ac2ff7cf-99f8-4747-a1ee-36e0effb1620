package com.tui.destilink.framework.aws.opensearch;

import lombok.RequiredArgsConstructor;
import org.apache.http.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.BasicHttpEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HttpContext;
import software.amazon.awssdk.auth.signer.Aws4Signer;
import software.amazon.awssdk.auth.signer.params.Aws4SignerParams;
import software.amazon.awssdk.http.ContentStreamProvider;
import software.amazon.awssdk.http.SdkHttpFullRequest;
import software.amazon.awssdk.http.SdkHttpMethod;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.function.Supplier;

import static org.apache.http.protocol.HttpCoreContext.HTTP_TARGET_HOST;

/**
 * AWS SDK v2 version of: https://github.com/awslabs/aws-request-signing-apache-interceptor/blob/master/src/main/java/com
 * /amazonaws/http/AWSRequestSigningApacheInterceptor.java
 */
@RequiredArgsConstructor
public class AWSRequestSigningInterceptor implements HttpRequestInterceptor {

    private final Aws4Signer signer; // NOSONAR migrate in the future
    private final Supplier<Aws4SignerParams> params;

    @Override
    public void process(final HttpRequest request, final HttpContext context) throws HttpException, IOException {
        URIBuilder uriBuilder;
        try {
            uriBuilder = new URIBuilder(request.getRequestLine().getUri());
        } catch (URISyntaxException e) {
            throw new IOException("Invalid URI", e);
        }

        final SdkHttpFullRequest.Builder signableRequestBuilder = SdkHttpFullRequest.builder();

        final HttpHost host = (HttpHost) context.getAttribute(HTTP_TARGET_HOST);
        if (host != null) {
            signableRequestBuilder.uri(URI.create(host.toURI()));
        }
        final SdkHttpMethod httpMethod =
                SdkHttpMethod.fromValue(request.getRequestLine().getMethod());
        signableRequestBuilder.method(httpMethod);
        try {
            signableRequestBuilder.encodedPath(uriBuilder.build().getRawPath());
        } catch (URISyntaxException e) {
            throw new IOException("Invalid URI", e);
        }

        if (request instanceof HttpEntityEnclosingRequest httpEntityEnclosingRequest && httpEntityEnclosingRequest.getEntity() != null) {
            final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            httpEntityEnclosingRequest.getEntity().writeTo(outputStream);
            signableRequestBuilder.contentStreamProvider(() -> new ByteArrayInputStream(outputStream.toByteArray()));
        }

        // Append Parameters and Headers
        nvpToMapParams(uriBuilder.getQueryParams()).forEach(signableRequestBuilder::appendRawQueryParameter);
        headerArrayToMap(request.getAllHeaders()).forEach(signableRequestBuilder::appendHeader);

        // Sign it
        final SdkHttpFullRequest signedRequest = signer.sign(signableRequestBuilder.build(), params.get());

        // Now copy everything back
        request.setHeaders(mapToHeaderArray(signedRequest.headers()));
        if (request instanceof HttpEntityEnclosingRequest httpEntityEnclosingRequest && httpEntityEnclosingRequest.getEntity() != null) {
            BasicHttpEntity basicHttpEntity = new BasicHttpEntity();
            Optional<ContentStreamProvider> contentStreamProvider = signedRequest.contentStreamProvider();
            if (contentStreamProvider.isPresent()) {
                basicHttpEntity.setContent(contentStreamProvider.get().newStream());
            } else {
                throw new IllegalStateException("Empty content stream was not expected!");
            }
            httpEntityEnclosingRequest.setEntity(basicHttpEntity);
        }
    }

    /**
     * @param params list of HTTP query params as NameValuePairs
     * @return a Multimap of HTTP query params
     */
    private static Map<String, String> nvpToMapParams(final List<NameValuePair> params) {
        Map<String, String> parameterMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        for (NameValuePair nvp : params) {
            parameterMap.putIfAbsent(nvp.getName(), nvp.getValue());
        }
        return parameterMap;
    }

    /**
     * @param headers modeled Header objects
     * @return a Map of header entries
     */
    private static Map<String, String> headerArrayToMap(final Header[] headers) {
        Map<String, String> headersMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        for (Header header : headers) {
            if (!skipHeader(header)) {
                headersMap.put(header.getName(), header.getValue());
            }
        }
        return headersMap;
    }

    /**
     * @param header header line to check
     * @return true if the given header should be excluded when signing
     */
    private static boolean skipHeader(final Header header) {
        // Strip Content-Length: 0
        var contentLength = "content-length".equalsIgnoreCase(header.getName()) && "0".equals(header.getValue());
        return contentLength || "host".equalsIgnoreCase(header.getName()); // Host comes from endpoint
    }

    /**
     * @param mapHeaders Map of header entries
     * @return modeled Header objects
     */
    private static Header[] mapToHeaderArray(final Map<String, List<String>> mapHeaders) {
        Header[] headers = new Header[mapHeaders.size()];
        int i = 0;
        for (Map.Entry<String, List<String>> headerEntry : mapHeaders.entrySet()) {
            for (String value : headerEntry.getValue()) {
                headers[i++] = new BasicHeader(headerEntry.getKey(), value);
            }
        }
        return headers;
    }

}
