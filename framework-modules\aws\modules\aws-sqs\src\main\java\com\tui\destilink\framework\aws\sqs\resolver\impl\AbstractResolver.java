package com.tui.destilink.framework.aws.sqs.resolver.impl;

import com.tui.destilink.framework.aws.sqs.resolver.SqsResolveException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.arns.Arn;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlResponse;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

@Slf4j
public abstract class AbstractResolver<T> {

    private static final Map<String, String> QUEUE_URL_CACHE = new ConcurrentHashMap<>();

    private final SqsAsyncClient sqsAsyncClient;

    private final Map<String, Optional<T>> cache = new ConcurrentHashMap<>();

    protected AbstractResolver(SqsAsyncClient sqsAsyncClient) {
        this.sqsAsyncClient = sqsAsyncClient;
    }

    protected CompletionStage<T> resolveWithCache(String queueNameArnUrl) {
        return resolveQueueUrl(queueNameArnUrl)
                .thenCompose(url -> {
                    if (cache.containsKey(url)) {
                        Optional<T> cachedValue = cache.get(url);
                        return CompletableFuture.completedFuture(cachedValue.orElse(null));
                    }
                    return resolveFromSource(url)
                            .thenApply(value -> {
                                cache.put(queueNameArnUrl, Optional.ofNullable(value));
                                return value;
                            });
                })
                .exceptionally(ex -> {
                    ex = extractCauseIfCompetition(ex);
                    if (ex instanceof SqsResolveException rex) {
                        throw rex;
                    }
                    throw new SqsResolveException("An unexpected error occurred while resolving for SQS queue" + queueNameArnUrl, ex);
                });
    }

    protected <R, E extends Throwable> BiConsumer<R, E> logException(String message) {
        return (r, t) -> {
            if (t != null) {
                log.error(message, extractCauseIfCompetition(t));
            }
        };
    }

    protected abstract CompletionStage<T> resolveFromSource(String queueUrl);

    protected SqsAsyncClient getSqsAsyncClient() {
        return sqsAsyncClient;
    }

    protected CompletionStage<String> resolveQueueUrl(String queueNameArnUrl) {
        try {
            if (queueNameArnUrl.startsWith("https://") || queueNameArnUrl.startsWith("http://")) {
                return CompletableFuture.completedFuture(queueNameArnUrl);
            }
            return resolveForNameOrArn(queueNameArnUrl);
        } catch (Exception ex) {
            return CompletableFuture.failedFuture(buildUrlResolveException(queueNameArnUrl, ex));
        }
    }

    protected Throwable extractCauseIfCompetition(Throwable t) {
        if (t instanceof CompletionException ce) {
            return ce.getCause();
        }
        return t;
    }

    private CompletionStage<String> resolveForNameOrArn(String queueArnName) {
        if (queueArnName.startsWith("arn:aws:sqs:")) {
            return resolveForName(Arn.fromString(queueArnName).resourceAsString());
        }
        return resolveForName(queueArnName);
    }

    private CompletionStage<String> resolveForName(String queueName) {
        String cachedValues = QUEUE_URL_CACHE.get(queueName);
        if (StringUtils.hasLength(cachedValues)) {
            return CompletableFuture.completedStage(cachedValues);
        }
        return sqsAsyncClient.getQueueUrl(b -> b.queueName(queueName))
                .thenApply(GetQueueUrlResponse::queueUrl)
                .whenComplete((url, ex) -> {
                    if (url != null) {
                        QUEUE_URL_CACHE.put(queueName, url);
                    }
                    if (ex != null) {
                        throw buildUrlResolveException(queueName, ex);
                    }
                });
    }

    private SqsResolveException buildUrlResolveException(String queue, Throwable t) {
        return new SqsResolveException("Failed to resolve URL for SQS queue " + queue, extractCauseIfCompetition(t));
    }
}
