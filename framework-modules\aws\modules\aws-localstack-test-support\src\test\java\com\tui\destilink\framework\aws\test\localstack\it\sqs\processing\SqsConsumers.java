package com.tui.destilink.framework.aws.test.localstack.it.sqs.processing;

import com.tui.destilink.framework.aws.sqs.listener.retry.SqsListenerRetryable;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.messaging.Message;

import java.util.Deque;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;

import static com.tui.destilink.framework.aws.test.localstack.it.sqs.processing.AbstractSqsProcessingIT.*;
import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.MESSAGING_PROPERTY_PREFIX;

@TestConfiguration
class SqsConsumers {

    public static final String SQS_LISTENER_MESSAGE_NONE = "none";
    public static final String SQS_LISTENER_MESSAGE_ACK = "ack";
    public static final String SQS_LISTENER_MESSAGE_ACK_ASYNC = "ack-async";
    public static final String SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN = "ack-async-join";
    public static final String SQS_LISTENER_MESSAGE_ACK_RETRYABLE = "ack-retryable";
    public static final String SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE = "no-ack-retryable";
    public static final String SQS_LISTENER_MESSAGE_EXCEPTION = "exception";
    public static final String SQS_LISTENER_MESSAGE_EXCEPTION_ACK = "exception-ack";
    public static final String SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE = "exception-ack-retryable";
    public static final String SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE = "exception-no-ack-retryable";

    public static final Deque<Message<?>> RECEIVED_MESSAGES_ORDERED = new ConcurrentLinkedDeque<>();
    public static final Map<String, Deque<Message<?>>> RECEIVED_MESSAGES = new ConcurrentHashMap<>();

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + QUEUE_NAME + ".name}", messageVisibilitySeconds = "10")
    public void queue(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        handle(message, retryable, ack);
    }

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + QUEUE_WITH_DLQ_NAME + ".name}", messageVisibilitySeconds = "10")
    public void queueWithDlq(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        handle(message, retryable, ack);
    }

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + QUEUE_WITH_DLQ_SINGLE_RETRY_NAME + ".name}", messageVisibilitySeconds = "10")
    public void queueWithDlqSingleRetry(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        handle(message, retryable, ack);
    }

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + QUEUE_FIFO_NAME + ".name}", messageVisibilitySeconds = "20", maxMessagesPerPoll = "2")
    public void queueFifo(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        handle(message, retryable, ack);
    }

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + QUEUE_WITH_DLQ_FIFO_NAME + ".name}", messageVisibilitySeconds = "20", maxMessagesPerPoll = "2")
    public void queueWithDlqFifo(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        handle(message, retryable, ack);
    }

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + QUEUE_WITH_DLQ_SINGLE_RETRY_FIFO_NAME + ".name}", messageVisibilitySeconds = "20", maxMessagesPerPoll = "2")
    public void queueWithDlqSingleRetryFifo(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        handle(message, retryable, ack);
    }

    private void handle(Message<?> message, SqsListenerRetryable retryable, Acknowledgement ack) {
        RECEIVED_MESSAGES_ORDERED.add(message);
        RECEIVED_MESSAGES.compute(message.getPayload().toString(), (k, v) -> {
            if (v == null) {
                v = new ConcurrentLinkedDeque<>();
            }
            v.add(message);
            return v;
        });
        switch (message.getPayload().toString()) {
            case SQS_LISTENER_MESSAGE_NONE:
                return;
            case SQS_LISTENER_MESSAGE_ACK:
                ack.acknowledge();
                return;
            case SQS_LISTENER_MESSAGE_ACK_ASYNC:
                ack.acknowledgeAsync();
                return;
            case SQS_LISTENER_MESSAGE_ACK_ASYNC_JOIN:
                ack.acknowledgeAsync().join();
                return;
            case SQS_LISTENER_MESSAGE_ACK_RETRYABLE:
                retryable.allowRetry();
                ack.acknowledge();
                return;
            case SQS_LISTENER_MESSAGE_NO_ACK_RETRYABLE:
                retryable.allowRetry();
                return;
            case SQS_LISTENER_MESSAGE_EXCEPTION:
                throw new RuntimeException(SQS_LISTENER_MESSAGE_EXCEPTION);
            case SQS_LISTENER_MESSAGE_EXCEPTION_ACK:
                ack.acknowledge();
                throw new RuntimeException(SQS_LISTENER_MESSAGE_EXCEPTION_ACK);
            case SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE:
                retryable.allowRetry();
                ack.acknowledge();
                throw new RuntimeException(SQS_LISTENER_MESSAGE_EXCEPTION_ACK_RETRYABLE);
            case SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE:
                retryable.allowRetryOnException(() -> {
                    throw new RuntimeException(SQS_LISTENER_MESSAGE_EXCEPTION_NO_ACK_RETRYABLE);
                });
                break;
            default:
                throw new UnsupportedOperationException(message.getPayload() + " is not supported");
        }
    }
}
