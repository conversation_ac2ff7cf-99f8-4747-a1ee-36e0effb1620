package com.tui.destilink.framework.aws.opensearch;

import com.tui.destilink.framework.aws.opensearch.config.OpenSearchConfigProperties;
import org.opensearch.spring.boot.autoconfigure.RestClientBuilderCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.tui.destilink.framework.aws.opensearch.config.OpenSearchConfigProperties.Http.Compression.PREFIX;

@Configuration
@EnableConfigurationProperties(OpenSearchConfigProperties.class)
public class OpenSearchHttpAutoConfiguration {

    /**
     * Compression does not work currently!
     *
     * @return
     */
    @ConditionalOnProperty(prefix = PREFIX, name = "enabled")
    @Bean
    public RestClientBuilderCustomizer compressionRestClientBuilderCustomizer() {
        return builder -> builder.setCompressionEnabled(true);
    }
}