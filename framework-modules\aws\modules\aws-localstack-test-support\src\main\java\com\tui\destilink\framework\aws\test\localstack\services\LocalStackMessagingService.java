package com.tui.destilink.framework.aws.test.localstack.services;

import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.annotations.SnsSubscription;
import com.tui.destilink.framework.aws.test.localstack.annotations.SnsTopic;
import com.tui.destilink.framework.aws.test.localstack.annotations.SqsQueue;
import com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver;
import com.tui.destilink.framework.test.support.core.util.TestSupportPropertiesUtils;
import io.awspring.cloud.autoconfigure.sns.SnsProperties;
import io.awspring.cloud.autoconfigure.sqs.SqsProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ConfigurableApplicationContext;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.*;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest;
import software.amazon.awssdk.services.sqs.model.CreateQueueResponse;
import software.amazon.awssdk.services.sqs.model.GetQueueAttributesResponse;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;

import java.util.*;

import static com.tui.destilink.framework.aws.test.localstack.services.LocalStackS3Service.CLOUDEVENTS_BUCKET_NAME;

@Slf4j
public class LocalStackMessagingService extends AbstractService {

    public static final String DLQ_SUFFIX = "-dlq";
    public static final String FIFO_SUFFIX = ".fifo";
    public static final String FIFO_DLQ_SUFFIX = "-dlq.fifo";

    private final TestSupportPropertiesUtils testSupportPropsUtils;

    private final Set<SqsQueue> sqsQueues;

    private final Set<SnsTopic> snsTopics;

    private final Set<SnsSubscription> snsSubscriptions;

    private final SqsClient sqsClient;

    private final SnsClient snsClient;
    private final Map<String, CreateQueueResponse> initializedSqsQueues = new HashMap<>();
    private final Map<String, CreateTopicResponse> initializedSnsTopics = new HashMap<>();
    private final Set<SubscribeResponse> initializedSubscriptions = new HashSet<>();
    private boolean initialized = false;

    public LocalStackMessagingService(LocalStack localStack, Set<SqsQueue> sqsQueues, Set<SnsTopic> snsTopics, Set<SnsSubscription> snsSubscriptions, ConfigurableApplicationContext context, String resourcePrefix) {
        super(localStack, context, resourcePrefix);
        this.testSupportPropsUtils = buildTestSupportPropUtils(context, "messaging");
        this.sqsQueues = sqsQueues;
        this.snsTopics = snsTopics;
        this.snsSubscriptions = snsSubscriptions;
        this.sqsClient = configureClientBuilder(SqsProperties.PREFIX, SqsProperties.class, SqsClient.builder()).build();
        this.snsClient = configureClientBuilder(SnsProperties.PREFIX, SnsProperties.class, SnsClient.builder()).build();
    }

    public static String buildDlqName(String queueName) {
        if (queueName.endsWith(FIFO_SUFFIX)) {
            return queueName.replace(FIFO_SUFFIX, FIFO_DLQ_SUFFIX);
        }
        return queueName + DLQ_SUFFIX;
    }

    @Override
    public void afterPropertiesSet() {
        if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
            this.destroy();
        }

        if (initialized || (sqsQueues.isEmpty() && snsTopics.isEmpty() && snsSubscriptions.isEmpty())) {
            return;
        }

        initialized = true;
        for (SqsQueue queue : this.sqsQueues) {
            createSqsQueue(queue);
        }
        for (SnsTopic topic : this.snsTopics) {
            createSnsTopic(topic);
        }
        for (SnsSubscription subscription : this.snsSubscriptions) {
            createSnsSubscription(subscription);
        }
    }

    @Override
    public void destroy() {
        if (getLocalStack().useNamePrefixes() && getLocalStack().cleanPrefixMatchingResources()) {
            // Delete subscriptions
            this.snsClient.listSubscriptions().subscriptions().stream()
                    .map(Subscription::subscriptionArn)
                    .filter(arn -> arn.contains(getResourcePrefix()))
                    .forEach(this::deleteSnsSubscription);
            // Delete sns topics
            this.snsClient.listTopics().topics().stream()
                    .map(Topic::topicArn)
                    .filter(arn -> arn.contains(getResourcePrefix()))
                    .forEach(this::deleteSnsTopic);
            // Delete sqs queues
            this.sqsClient.listQueues(b -> b.queueNamePrefix(getResourcePrefix())).queueUrls()
                    .forEach(this::deleteSqsQueue);
        } else if (initialized) {
            for (SubscribeResponse subscribeResponse : this.initializedSubscriptions) {
                deleteSnsSubscription(subscribeResponse.subscriptionArn());
            }
            for (CreateTopicResponse topic : this.initializedSnsTopics.values()) {
                deleteSnsTopic(topic.topicArn());
            }
            for (CreateQueueResponse queue : this.initializedSqsQueues.values()) {
                deleteSqsQueue(queue.queueUrl());
            }
        }
        this.initialized = false;
    }

    private void createSqsQueue(SqsQueue queue) {
        String dlqArn = null;
        if (queue.createDlq()) {
            if (queue.name().endsWith(FIFO_SUFFIX)) {
                dlqArn = createSqsQueue(queue.name().replace(FIFO_SUFFIX, FIFO_DLQ_SUFFIX), null, null);
            } else {
                dlqArn = createSqsQueue(queue.name() + DLQ_SUFFIX, null, null);
            }
        }
        if (queue.name().endsWith(FIFO_SUFFIX)) {
            createSqsQueue(queue.name(), dlqArn, queue.maxReceiveCountFifo());
        } else {
            createSqsQueue(queue.name(), dlqArn, queue.maxReceiveCount());
        }
    }

    private String createSqsQueue(String queueName, String dlqArn, Integer maxReceiveCount) {
        String prefixedName = generateResourceName(queueName);
        CreateQueueRequest.Builder req = CreateQueueRequest.builder()
                .queueName(prefixedName);
        Map<QueueAttributeName, String> queueAttributes = new EnumMap<>(QueueAttributeName.class);
        if (queueName.endsWith(FIFO_SUFFIX)) {
            queueAttributes.put(QueueAttributeName.FIFO_QUEUE, "true");
        }
        if (dlqArn != null) {
            queueAttributes.put(QueueAttributeName.REDRIVE_POLICY, "{\"maxReceiveCount\": \"" + maxReceiveCount + "\", \"deadLetterTargetArn\":\"" + dlqArn + "\"}");
        }
        if (!queueAttributes.isEmpty()) {
            req.attributes(queueAttributes);
        }
        CreateQueueResponse res = this.sqsClient.createQueue(req.build());
        this.initializedSqsQueues.put(queueName, res);
        GetQueueAttributesResponse attributes = this.sqsClient.getQueueAttributes(
                b -> b.queueUrl(res.queueUrl()).attributeNames(QueueAttributeName.QUEUE_ARN)
        );
        TestSupportPropertiesUtils forQueue = testSupportPropsUtils.forNestedPropertyKey(queueName);
        forQueue.addProperty(prefixedName, LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_NAME);
        forQueue.addProperty(res.queueUrl(), LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_URL);
        forQueue.addProperty(attributes.attributes().get(QueueAttributeName.QUEUE_ARN), LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_ARN);
        return attributes.attributes().get(QueueAttributeName.QUEUE_ARN);
    }

    private void createSnsTopic(SnsTopic topic) {
        String prefixedName = generateResourceName(topic.name());
        CreateTopicRequest.Builder req = CreateTopicRequest.builder()
                .name(prefixedName);
        if (topic.isCloudEvent()) {
            req.tags(addCloudEventTags(prefixedName));
        }
        if (topic.name().endsWith(FIFO_SUFFIX)) {
            req.attributes(Map.of("FifoTopic", "true"));
        }
        CreateTopicResponse res = this.snsClient.createTopic(req.build());
        this.initializedSnsTopics.put(topic.name(), res);
        TestSupportPropertiesUtils forTopic = testSupportPropsUtils.forNestedPropertyKey(topic.name());
        forTopic.addProperty(prefixedName, LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_NAME);
        forTopic.addProperty(res.topicArn(), LocalStackPropertiesResolver.PROPERTY_KEY_SUFFIX_ARN);
    }

    private List<Tag> addCloudEventTags(String topicName) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.builder().key("cloudevents-s3-accesspoint-alias").value(generateResourceName(CLOUDEVENTS_BUCKET_NAME)).build());
        tags.add(Tag.builder().key("cloudevents-s3-prefix").value(topicName).build());
        tags.add(Tag.builder().key("cloudevents-type").value("internal").build());
        return tags;
    }

    private void createSnsSubscription(SnsSubscription subscription) {
        CreateQueueResponse queue = this.initializedSqsQueues.get(subscription.sqsQueueName());
        CreateTopicResponse topic = this.initializedSnsTopics.get(subscription.snsTopicName());
        if (queue == null || topic == null) {
            log.error("Failed to create SnsSubscription {}", subscription);
            throw new IllegalStateException("Failed to create SnsSubscription " + subscription);
        }
        String queueArn = this.sqsClient.getQueueAttributes(b -> b.queueUrl(queue.queueUrl()).attributeNames(QueueAttributeName.ALL)).attributes().get(QueueAttributeName.QUEUE_ARN);
        Map<String, String> attributes = new HashMap<>();
        if (subscription.rawMessageDelivery()) {
            attributes.put("RawMessageDelivery", "true");
        }
        SubscribeRequest.Builder req = SubscribeRequest.builder()
                .topicArn(topic.topicArn())
                .endpoint(queueArn)
                .protocol("sqs")
                .attributes(attributes);
        SubscribeResponse res = this.snsClient.subscribe(req.build());
        this.initializedSubscriptions.add(res);
    }

    private void deleteSqsQueue(String queueUrl) {
        try {
            this.sqsClient.deleteQueue(b -> b.queueUrl(queueUrl));
        } catch (Exception ex) {
            log.error("Failed to delete Queue {}", queueUrl, ex);
        }
    }

    private void deleteSnsTopic(String topicArn) {
        try {
            this.snsClient.deleteTopic(b -> b.topicArn(topicArn));
        } catch (Exception ex) {
            log.error("Failed to delete Topic {}", topicArn, ex);
        }
    }

    private void deleteSnsSubscription(String subscriptionArn) {
        try {
            this.snsClient.unsubscribe(b -> b.subscriptionArn(subscriptionArn));
        } catch (Exception ex) {
            log.error("Failed to delete SnsSubscription {}", subscriptionArn, ex);
        }
    }
}
