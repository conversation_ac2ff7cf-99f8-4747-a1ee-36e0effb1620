package com.tui.destilink.framework.aws.test.localstack.services;

import com.tui.destilink.framework.aws.test.localstack.annotations.LocalStack;
import com.tui.destilink.framework.aws.test.localstack.util.AwsClientBuilderConfigurer;
import com.tui.destilink.framework.test.support.core.util.TestSupportPropertiesUtils;
import io.awspring.cloud.autoconfigure.AwsClientProperties;
import lombok.AccessLevel;
import lombok.Getter;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import software.amazon.awssdk.awscore.client.builder.AwsClientBuilder;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.LOCALSTACK;

public abstract class AbstractService implements InitializingBean, DisposableBean {

    @Getter(AccessLevel.PROTECTED)
    private final LocalStack localStack;

    @Getter(AccessLevel.PROTECTED)
    private final ConfigurableApplicationContext context;

    @Getter(AccessLevel.PROTECTED)
    private final ConfigurableEnvironment environment;

    @Getter(AccessLevel.PROTECTED)
    private final String resourcePrefix;

    private final Binder binder;

    private final AwsClientBuilderConfigurer awsClientBuilderConfigurer;

    protected AbstractService(LocalStack localStack, ConfigurableApplicationContext context, String resourcePrefix) {
        this.localStack = localStack;
        this.context = context;
        this.environment = context.getEnvironment();
        this.resourcePrefix = resourcePrefix;
        this.binder = Binder.get(context.getEnvironment());
        this.awsClientBuilderConfigurer = new AwsClientBuilderConfigurer(this.binder);
    }

    protected <T> T loadProperties(String name, Class<T> target) {
        return binder.bindOrCreate(name, target);
    }

    protected <T extends AwsClientBuilder<?, ?>, P extends AwsClientProperties> T configureClientBuilder(String propertiesPrefix, Class<P> propertiesClass, T clientBuilder) {
        if (propertiesClass != null) {
            final P properties = loadProperties(propertiesPrefix, propertiesClass);
            return awsClientBuilderConfigurer.configure(clientBuilder, properties);
        } else {
            return awsClientBuilderConfigurer.configure(clientBuilder, null);
        }
    }

    protected String generateResourceName(String name) {
        return resourcePrefix + name;
    }

    protected <T> T bindProperties(String name, Class<T> clazz) {
        return Binder.get(environment).bindOrCreate(name, clazz);
    }

    protected static TestSupportPropertiesUtils buildTestSupportPropUtils(ConfigurableApplicationContext context, String propertySuffix) {
        return TestSupportPropertiesUtils.forPropertyKey(context, LOCALSTACK, propertySuffix);
    }
}
