package com.tui.destilink.framework.aws.sqs.tracing.execution_interceptor;

import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.util.GlobalTracer;
import lombok.experimental.UtilityClass;
import org.springframework.lang.Nullable;
import software.amazon.awssdk.services.sqs.model.Message;

import static com.tui.destilink.framework.aws.sqs.tracing.execution_interceptor.ReceiveMessageTraceInterceptor.*;
import static io.awspring.cloud.sqs.listener.SqsHeaders.SQS_SOURCE_DATA_HEADER;

@UtilityClass
public class SqsMessageTraceUtils {

    @Nullable
    public static Span getRootSpan(org.springframework.messaging.Message<?> message) {
        Message sqsMsg = extractSqsMessage(message);
        if (sqsMsg != null) {
            return getRootSpan(sqsMsg);
        }
        return null;
    }

    @Nullable
    public static Span getRootSpan(Message message) {
        if (message != null) {
            return FW_RECEIVE_ROOT_SPAN_MAP.get(message);
        }
        return null;
    }

    @Nullable
    public static Scope activateRootSpan(org.springframework.messaging.Message<?> message) {
        Message sqsMsg = extractSqsMessage(message);
        if (sqsMsg != null) {
            return activateRootSpan(sqsMsg);
        }
        return null;
    }

    @Nullable
    public static Scope activateRootSpan(Message message) {
        if (message != null) {
            Span span = FW_RECEIVE_ROOT_SPAN_MAP.get(message);
            if (span != null) {
                return GlobalTracer.get().activateSpan(span);
            }
        }
        return null;
    }

    public static void finish(org.springframework.messaging.Message<?> message) {
        Message sqsMsg = extractSqsMessage(message);
        if (sqsMsg != null) {
            finish(sqsMsg);
        }
    }

    public static void finish(Message message) {
        if (message != null) {
            Scope scope = FW_RECEIVE_ROOT_SPAN_SCOPE_MAP.remove(message);
            Span fwRootSpan = FW_RECEIVE_ROOT_SPAN_MAP.remove(message);
            Span datadogRootSpan = DATADOG_RECEIVE_ROOT_SPAN_MAP.remove(message);
            if (scope != null) {
                scope.close();
            }
            if (fwRootSpan != null) {
                fwRootSpan.finish();
            }
            if (datadogRootSpan != null) {
                datadogRootSpan.finish();
            }
        }
    }

    private Message extractSqsMessage(org.springframework.messaging.Message<?> message) {
        if (message != null) {
            Object value = message.getHeaders().get(SQS_SOURCE_DATA_HEADER);
            if (value instanceof Message m) {
                return m;
            }
        }
        return null;
    }
}
