package com.tui.destilink.framework.aws.sns.logger;

import com.tui.destilink.framework.aws.sns.config.DefaultSnsContainerProperties.SnsLogging;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import software.amazon.awssdk.services.sns.model.PublishRequest;

import java.util.Collections;

class SnsLogWriterTest {

    @Test
    void givenMessageLoggingEnabledShouldCallFormatter() {
        //given
        SnsLogFormatter mock = Mockito.mock(SnsLogFormatter.class);

        //when
        final PublishRequest request = PublishRequest.builder().topicArn("t").message("message").build();
        getSnsLogWriter(mock, true).write("1", request);

        //then
        Mockito.verify(mock).format("1", Collections.emptyMap(), request);
    }

    @Test
    void givenMessageLoggingDisabledShouldNotCallFormatter() {
        //given
        SnsLogFormatter mock = Mockito.mock(SnsLogFormatter.class);

        //when
        getSnsLogWriter(mock, false).write("", PublishRequest.builder().build());

        //then
        Mo<PERSON>to.verifyNoInteractions(mock);
    }

    private static SnsLogWriter getSnsLogWriter(SnsLogFormatter mock, boolean enabled) {
        SnsLogging snsLogging = new SnsLogging();
        snsLogging.setEnabled(enabled);
        return new SnsLogWriter(snsLogging, mock);
    }
}