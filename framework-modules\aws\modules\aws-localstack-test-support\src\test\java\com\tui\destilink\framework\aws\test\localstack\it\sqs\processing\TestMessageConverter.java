package com.tui.destilink.framework.aws.test.localstack.it.sqs.processing;

import com.tui.destilink.framework.core.messaging.messages.NullMessage;
import com.tui.destilink.framework.core.messaging.messages.RetryableErrorMessage;
import io.awspring.cloud.sqs.support.converter.MessageConversionContext;
import io.awspring.cloud.sqs.support.converter.SqsMessagingMessageConverter;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.ErrorMessage;
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName;

import java.util.Deque;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;

public class TestMessageConverter extends SqsMessagingMessageConverter {

    public static final String SQS_CONVERTER_HEADER_NAME = "sqs-converter-test-header";

    public static final String SQS_CONVERTER_HEADER_SUCCESS = "success";
    public static final String SQS_CONVERTER_HEADER_NULL_MESSAGE = "null";

    public static final String SQS_CONVERTER_HEADER_EXCEPTION = "exception";
    public static final String SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND = "exception-success-second";
    public static final String SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD = "exception-success-third";

    public static final String SQS_CONVERTER_HEADER_ERROR_MESSAGE = "error-message";

    public static final String SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE = "retryable-message";
    public static final String SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND = "retryable-message-success-second";
    public static final String SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD = "retryable-message-success-third";

    public static final Deque<software.amazon.awssdk.services.sqs.model.Message> INBOUND_MESSAGES_ORDERED = new ConcurrentLinkedDeque<>();
    public static final Map<String, Deque<software.amazon.awssdk.services.sqs.model.Message>> CONVERTED_MESSAGES = new ConcurrentHashMap<>();

    public static Map<String, MessageAttributeValue> buildConverterHeaderMap(String value) {
        return Map.of(SQS_CONVERTER_HEADER_NAME, MessageAttributeValue.builder().dataType("String").stringValue(value).build());
    }

    @Override
    public Message<?> toMessagingMessage(software.amazon.awssdk.services.sqs.model.Message source, MessageConversionContext context) {
        INBOUND_MESSAGES_ORDERED.add(source);
        Message<?> message = super.toMessagingMessage(source, context);
        CONVERTED_MESSAGES.compute(source.messageAttributes().get(SQS_CONVERTER_HEADER_NAME).stringValue(), (k, v) -> {
            if (v == null) {
                v = new ConcurrentLinkedDeque<>();
            }
            v.add(source);
            return v;
        });
        return switch (source.messageAttributes().get(SQS_CONVERTER_HEADER_NAME).stringValue()) {
            case SQS_CONVERTER_HEADER_SUCCESS -> message;
            case SQS_CONVERTER_HEADER_NULL_MESSAGE -> NullMessage.INSTANCE;
            case SQS_CONVERTER_HEADER_EXCEPTION -> throw new RuntimeException(SQS_CONVERTER_HEADER_EXCEPTION);
            case SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND -> {
                if (approxReceiveCount(source) > 1) {
                    yield message;
                }
                throw new RuntimeException(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND);
            }
            case SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_THIRD -> {
                if (approxReceiveCount(source) > 2) {
                    yield message;
                }
                throw new RuntimeException(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND);
            }
            case SQS_CONVERTER_HEADER_ERROR_MESSAGE ->
                    new ErrorMessage(new RuntimeException(SQS_CONVERTER_HEADER_EXCEPTION_SUCCESS_SECOND));
            case SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE ->
                    new RetryableErrorMessage(new RuntimeException(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE));
            case SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND -> {
                if (approxReceiveCount(source) > 1) {
                    yield message;
                }
                yield new RetryableErrorMessage(new RuntimeException(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_SECOND));
            }
            case SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD -> {
                if (approxReceiveCount(source) > 2) {
                    yield message;
                }
                yield new RetryableErrorMessage(new RuntimeException(SQS_CONVERTER_HEADER_RETRYABLE_MESSAGE_SUCCESS_THIRD));
            }
            default ->
                    throw new UnsupportedOperationException(source.messageAttributes().get(SQS_CONVERTER_HEADER_NAME).stringValue() + " is not supported");
        };
    }

    private int approxReceiveCount(software.amazon.awssdk.services.sqs.model.Message source) {
        return Optional.ofNullable(source.attributes().get(MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT))
                .map(Integer::parseInt).orElseThrow();
    }
}
