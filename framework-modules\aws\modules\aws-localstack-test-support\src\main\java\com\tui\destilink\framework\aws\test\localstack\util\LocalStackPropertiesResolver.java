package com.tui.destilink.framework.aws.test.localstack.util;

import com.tui.destilink.framework.aws.test.localstack.services.LocalStackMessagingService;
import com.tui.destilink.framework.core.util.FwPropertySourceUtils;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.MapPropertySource;

import static com.tui.destilink.framework.test.support.core.util.TestSupportPropertiesUtils.PROPERTY_KEY_SEPARATOR;
import static com.tui.destilink.framework.test.support.core.util.TestSupportPropertiesUtils.TEST_SUPPORT_PROPERTIES_PREFIX;

public class LocalStackPropertiesResolver {

    public static final String LOCALSTACK = "localstack";
    public static final String S3 = "s3";
    public static final String S3_PROPERTY_PREFIX = TEST_SUPPORT_PROPERTIES_PREFIX + PROPERTY_KEY_SEPARATOR + LOCALSTACK + PROPERTY_KEY_SEPARATOR + S3 + PROPERTY_KEY_SEPARATOR;
    public static final String MESSAGING = "messaging";
    public static final String MESSAGING_PROPERTY_PREFIX = TEST_SUPPORT_PROPERTIES_PREFIX + PROPERTY_KEY_SEPARATOR + LOCALSTACK + PROPERTY_KEY_SEPARATOR + MESSAGING + PROPERTY_KEY_SEPARATOR;

    public static final String PROPERTY_KEY_SUFFIX_NAME = "name";
    public static final String PROPERTY_KEY_SUFFIX_ARN = "arn";
    public static final String PROPERTY_KEY_SUFFIX_URL = "url";

    private final MapPropertySource properties;

    public LocalStackPropertiesResolver(ConfigurableApplicationContext context) {
        this.properties = FwPropertySourceUtils.getFwPropertiesOverridePropertySource(context.getEnvironment());
    }

    public String getQueueName(String queueName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + queueName + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_NAME);
    }

    public String getQueueUrl(String queueName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + queueName + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_URL);
    }

    public String getQueueArn(String queueName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + queueName + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_ARN);
    }

    public String getQueueDlqName(String queueName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + LocalStackMessagingService.buildDlqName(queueName) + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_NAME);
    }

    public String getQueueDlqUrl(String queueName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + LocalStackMessagingService.buildDlqName(queueName) + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_URL);
    }

    public String getQueueDlqArn(String queueName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + LocalStackMessagingService.buildDlqName(queueName) + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_ARN);
    }

    public String getTopicName(String topicName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + topicName + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_NAME);
    }


    public String getTopicArn(String topicName) {
        return getOrNull(MESSAGING_PROPERTY_PREFIX + topicName + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_ARN);
    }

    public String getBucketName(String bucketName) {
        return getOrNull(S3_PROPERTY_PREFIX + bucketName + PROPERTY_KEY_SEPARATOR + PROPERTY_KEY_SUFFIX_NAME);
    }

    private String getOrNull(String key) {
        Object value = properties.getProperty(key);
        if (value != null) {
            return value.toString();
        }
        return null;
    }
}
