package com.tui.destilink.framework.aws.sqs;

import com.tui.destilink.framework.aws.sqs.listener.invoke.TracingMessageHandlerInvokeInterceptor;
import com.tui.destilink.framework.aws.sqs.tracing.execution_interceptor.ReceiveMessageTraceInterceptor;
import com.tui.destilink.framework.core.messaging.message_handler.aspect.InterceptingMessageHandlerAspect;
import com.tui.destilink.framework.core.messaging.message_handler.aspect.InterceptingMessageHandlerMethodFactory;
import io.awspring.cloud.autoconfigure.sqs.SqsAsyncClientCustomizer;
import io.awspring.cloud.sqs.config.SqsBootstrapConfiguration;
import io.awspring.cloud.sqs.config.SqsListenerConfigurer;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.SqsAsyncClientBuilder;

@AutoConfiguration
@ConditionalOnClass({SqsAsyncClient.class, SqsBootstrapConfiguration.class})
@ConditionalOnProperty(name = "spring.cloud.aws.sqs.enabled", havingValue = "true", matchIfMissing = true)
public class SqsTracingAutoConfiguration {

    @Bean
    SqsListenerConfigurer invokeInterceptorSqsListenerConfigurer(ObjectProvider<InterceptingMessageHandlerAspect> invokes) {
        return registry -> registry
                .setMessageHandlerMethodFactory(new InterceptingMessageHandlerMethodFactory(invokes));
    }

    @Bean
    SqsAsyncClientCustomizer sqsAsyncClientCustomizer() {
        return builder -> {
            builder.overrideConfiguration(builder.overrideConfiguration().copy(c -> {
                c.addExecutionInterceptor(new ReceiveMessageTraceInterceptor());
            }));
        };
    }

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    TracingMessageHandlerInvokeInterceptor tracingMessageHandlerInvokeInterceptor(SqsAsyncClient sqsAsyncClient) {
        return new TracingMessageHandlerInvokeInterceptor(sqsAsyncClient);
    }
}
