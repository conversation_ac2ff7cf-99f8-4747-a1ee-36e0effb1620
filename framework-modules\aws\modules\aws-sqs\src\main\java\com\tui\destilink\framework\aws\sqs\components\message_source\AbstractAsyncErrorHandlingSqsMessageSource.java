package com.tui.destilink.framework.aws.sqs.components.message_source;

import com.tui.destilink.framework.aws.sqs.config.DefaultSqsContainerProperties;
import com.tui.destilink.framework.aws.sqs.config.SqsLoggingProperties;
import com.tui.destilink.framework.aws.sqs.listener.retry.SqsListenerRetryableHandler;
import com.tui.destilink.framework.aws.sqs.logging.SqsLoggingContextCustomizer;
import com.tui.destilink.framework.aws.sqs.logging.context.SqsMessageContextDecorator;
import com.tui.destilink.framework.aws.sqs.logging.marker.SqsMessageMarker;
import com.tui.destilink.framework.aws.sqs.resolver.QueueUrlResolver;
import com.tui.destilink.framework.aws.sqs.tracing.execution_interceptor.SqsMessageTraceUtils;
import com.tui.destilink.framework.aws.sqs.util.DeadLetterQueueUtils;
import com.tui.destilink.framework.core.logging.context.TripsContextDecorator;
import com.tui.destilink.framework.core.logging.context.util.ContextSnapshotUtils;
import com.tui.destilink.framework.core.logging.monitoring.MonitoringMarkerUtils;
import com.tui.destilink.framework.core.logging.monitoring.MonitoringMoodData;
import com.tui.destilink.framework.core.messaging.message.util.IdTsAwareMessageUtils;
import com.tui.destilink.framework.core.messaging.messages.NullMessage;
import com.tui.destilink.framework.core.messaging.messages.RetryableErrorMessage;
import com.tui.destilink.framework.core.tracing.TracingUtils;
import com.tui.destilink.framework.core.util.FutureUtils;
import datadog.trace.api.DDTags;
import io.awspring.cloud.sqs.ConfigUtils;
import io.awspring.cloud.sqs.MessagingHeaders;
import io.awspring.cloud.sqs.listener.ContainerOptions;
import io.awspring.cloud.sqs.listener.QueueAttributesAware;
import io.awspring.cloud.sqs.listener.SqsContainerOptions;
import io.awspring.cloud.sqs.listener.SqsHeaders;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import io.awspring.cloud.sqs.listener.source.AbstractSqsMessageSource;
import io.awspring.cloud.sqs.support.converter.MessageConversionContext;
import io.awspring.cloud.sqs.support.converter.SqsMessageConversionContext;
import io.awspring.cloud.sqs.support.converter.SqsMessagingMessageConverter;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.util.GlobalTracer;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.ErrorMessage;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import java.util.*;
import java.util.concurrent.*;

import static com.tui.destilink.framework.aws.sqs.util.MessagingMessageHeadersUtils.MESSAGE_HEADER_RETRYABLE_HANDLER;
import static com.tui.destilink.framework.core.logging.context.util.MessagingMessageContextSnapshotUtils.CONTEXT_SNAPSHOT_HEADER;
import static java.util.stream.Collectors.groupingBy;

@Slf4j
public abstract class AbstractAsyncErrorHandlingSqsMessageSource<T> extends AbstractSqsMessageSource<T> {

    private static final int MAX_MESSAGES_PER_REQUEST_DEFAULT = 10;

    public static final String CONVERT_SPAN_OPERATION_NAME = "destilink.framework.sqs.convert";
    public static final String CONVERT_MESSAGE_SPAN_RESOURCE_NAME = "Framework.Sqs.ConvertMessage";

    private final ExecutorService executor;

    private final List<SqsLoggingContextCustomizer> loggingContextCustomizers;
    private final boolean loggingEnabled;

    private final int maxRetriesNoDlq;

    private final QueueUrlResolver queueUrlResolver;

    private int maxMessagesPerRequest = MAX_MESSAGES_PER_REQUEST_DEFAULT;

    private SqsAsyncClient sqsAsyncClient;
    private SqsMessagingMessageConverter messagingMessageConverter;

    private ExtendedQueueAttributes extendedQueueAttributes;
    private ExtendedQueueAttributes.RedrivePolicy redrivePolicy;
    private String queueUrl;

    protected AbstractAsyncErrorHandlingSqsMessageSource(ObjectProvider<SqsLoggingContextCustomizer> loggingContextCustomizers, DefaultSqsContainerProperties defaultSqsContainerProperties, SqsLoggingProperties properties, QueueUrlResolver queueUrlResolver) {
        this.loggingContextCustomizers = loggingContextCustomizers.orderedStream().toList();
        this.maxRetriesNoDlq = defaultSqsContainerProperties.getMaxRetriesNoDlq();
        this.loggingEnabled = properties.getEnabled();
        this.queueUrlResolver = queueUrlResolver;
        ThreadFactory threadFactory = Thread.ofVirtual().name(String.format("%s-", this.getClass().getName()), 0).factory();
        this.executor = Executors.newThreadPerTaskExecutor(threadFactory);
    }

    @Override
    protected void doConfigure(ContainerOptions<?, ?> containerOptions) {
        super.doConfigure(containerOptions);
        Assert.isInstanceOf(SqsContainerOptions.class, containerOptions,
                "containerOptions must be an instance of SqsContainerOptions");
        Assert.isInstanceOf(SqsMessagingMessageConverter.class, containerOptions.getMessageConverter(),
                "messageConverter must be an instance of SqsMessagingMessageConverter");
        this.messagingMessageConverter = (SqsMessagingMessageConverter) containerOptions.getMessageConverter();
        this.maxMessagesPerRequest = containerOptions.getMaxMessagesPerPoll();
    }

    @Override
    protected void doStart() {
        Assert.isInstanceOf(SqsMessageConversionContext.class, getMessageConversionContext());
        super.doStart();
        extendedQueueAttributes = ExtendedQueueAttributes.of(getMessageConversionContext(), queueUrlResolver);
        ConfigUtils.INSTANCE
                .acceptIfInstance(getMessageConversionContext(), QueueAttributesAware.class,
                        qaa -> qaa.setQueueAttributes(extendedQueueAttributes));
        queueUrl = extendedQueueAttributes.getQueueUrl();
        redrivePolicy = extendedQueueAttributes.getRedrivePolicy();
    }

    @Override
    public void setSqsAsyncClient(SqsAsyncClient sqsAsyncClient) {
        super.setSqsAsyncClient(sqsAsyncClient);
        this.sqsAsyncClient = sqsAsyncClient;
    }

    @Override
    @NonNull
    protected SqsMessageConversionContext getMessageConversionContext() {
        MessageConversionContext context = super.getMessageConversionContext();
        Assert.notNull(context, "messageConversionContext must not be null");
        return (SqsMessageConversionContext) context;
    }

    protected ExtendedQueueAttributes getExtendedQueueAttributes() {
        return extendedQueueAttributes;
    }

    protected ExtendedQueueAttributes.RedrivePolicy getRedrivePolicy() {
        return redrivePolicy;
    }

    @Override
    protected Collection<Message<T>> convertMessages(Collection<software.amazon.awssdk.services.sqs.model.Message> messages) {
        Map<String, List<software.amazon.awssdk.services.sqs.model.Message>> groups = messages.stream().collect(groupingBy(this::getGroupId));
        List<CompletableFuture<Collection<Message<T>>>> futures = groups.entrySet().stream()
                .map(g -> CompletableFuture.supplyAsync(() -> convertMessageGroup(g.getKey(), g.getValue()), executor))
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().map(CompletableFuture::join).toList())
                .exceptionally(ex -> {
                    log.error("An unexpected error occurred while processing message groups", ex);
                    return Collections.emptyList();
                })
                .join().stream()
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .toList();
    }

    protected String getGroupId(software.amazon.awssdk.services.sqs.model.Message message) {
        String messageGroupId = message.attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID);
        if (StringUtils.hasText(messageGroupId)) {
            return messageGroupId;
        }
        return message.messageId();
    }

    protected Collection<Message<T>> convertMessageGroup(String messageGroupId, List<software.amazon.awssdk.services.sqs.model.Message> messages) {
        List<Message<T>> result = new ArrayList<>(messages.size());
        try {
            ListIterator<software.amazon.awssdk.services.sqs.model.Message> messagesIterator = messages.listIterator();
            while (messagesIterator.hasNext()) {
                software.amazon.awssdk.services.sqs.model.Message message = messagesIterator.next();
                messagesIterator.remove();
                result.add(convertMessage(message));
            }
        } catch (SqsMessageGroupSkippingException ex) {
            if (messages.size() > 1) {
                log.error("Skipping pending {} messages from group id={} after receiving an error while processing message with index {}", (messages.size() - result.size() - 1), messageGroupId, (result.size()));
            }
        } catch (Exception ex) {
            log.error("An unexpected error occurred while processing message group id={} of size={}", messageGroupId, messages.size(), ex);
            TracingUtils.setErrorOnSpan(ex, log, true);
        }
        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    protected Message<T> convertMessage(software.amazon.awssdk.services.sqs.model.Message message) {
        Message<T> result = null;
        try (Scope ignoreParentScope = SqsMessageTraceUtils.activateRootSpan(message)) {
            Span childSpan = buildChildSpan();
            try (Scope ignoreChildScope = GlobalTracer.get().activateSpan(childSpan)) {
                SqsListenerRetryableHandler retryableHandler = null;
                try {
                    SqsMessageContextDecorator.decorate(message, getMessageConversionContext());
                    decorateTripsContextFromHeaders(message);
                    retryableHandler = buildSqsListenerRetryableHandler(message);
                    Message<?> resMsg = convertMessageInternal(message, retryableHandler);
                    if (resMsg == null || resMsg instanceof NullMessage) {
                        return null;
                    }
                    decorateLoggingContext(resMsg);
                    result = enrichContext((Message<T>) resMsg, retryableHandler);
                    return result;
                } catch (SqsMessageGroupSkippingException ex) {
                    throw ex;
                } catch (Exception ex) {
                    result = null;
                    TracingUtils.setErrorOnSpan(ex, log, true);
                    log.error("An unexpected error occurred while processing message id={} from queue={}", message.messageId(), queueUrl, ex);
                    handleErrorMessage(message, retryableHandler);
                } finally {
                    if (result != null && loggingEnabled) {
                        log.info(SqsMessageMarker.sqsMessageMarker(message, getMessageConversionContext()),
                                "Received SQS message id={} from queue={}",
                                message.messageId(), queueUrl);
                    }
                }
            } finally {
                if (childSpan != null) {
                    childSpan.finish();
                }
            }
        } finally {
            if (result == null) {
                SqsMessageTraceUtils.finish(message);
            }
            MDC.clear();
        }
        return null;
    }

    @SuppressWarnings("ConstantConditions")
    private Message<?> /* NOSONAR */ convertMessageInternal(software.amazon.awssdk.services.sqs.model.Message message, SqsListenerRetryableHandler retryableHandler) {
        Message<?> result;
        try {
            result = this.messagingMessageConverter.toMessagingMessage(message, getMessageConversionContext());
        } catch (Exception ex) {
            log.error("An unexpected error occurred while converting message id={} from queue={}", message.messageId(), queueUrl, ex);
            result = new ErrorMessage(ex);
        }
        if (result == null || result instanceof NullMessage) {
            handleNullMessage(message, retryableHandler);
        } else if (result instanceof RetryableErrorMessage rem) {
            TracingUtils.setErrorOnSpan(rem.getPayload(), log, true);
            handleRetryableErrorMessage(rem, message, retryableHandler);
        } else if (result instanceof ErrorMessage em) {
            TracingUtils.setErrorOnSpan(em.getPayload(), log, true);
            handleErrorMessage(message, retryableHandler);
        } else {
            return result;
        }
        return NullMessage.INSTANCE;
    }

    protected void handleNullMessage(software.amazon.awssdk.services.sqs.model.Message sourceMessage, SqsListenerRetryableHandler retryableHandler) {
        Message<?> deleteResult = deleteMessage(sourceMessage);
        if (deleteResult instanceof NullMessage) {
            return;
        }
        if (extendedQueueAttributes.isFifo()) {
            if (retryableHandler.isRetryPossible()) {
                throw new SqsMessageGroupSkippingException();
            }
            logFifoOrderNoLongerGuaranteed(sourceMessage);
        }
    }

    protected void handleRetryableErrorMessage(RetryableErrorMessage errorMessage, software.amazon.awssdk.services.sqs.model.Message sourceMessage, SqsListenerRetryableHandler retryableHandler) {
        if (errorMessage.isRetryable() && retryableHandler.isRetryPossible()) {
            log.info("Retrying message id={} from queue={} when the visibility timeout expired", sourceMessage.messageId(), queueUrl);
            throw new SqsMessageGroupSkippingException();
        }
        handleErrorMessage(sourceMessage, retryableHandler);
    }

    protected void handleErrorMessage(software.amazon.awssdk.services.sqs.model.Message sourceMessage, SqsListenerRetryableHandler sqsListenerRetryableHandler) {
        // Try forward to DLQ
        Message<?> dlqResult = forwardDeadLetterQueue(sourceMessage);
        if (extendedQueueAttributes.isFifo()) {
            // Always try to ack for FIFO
            handleNullMessage(sourceMessage, sqsListenerRetryableHandler);
        } else if (dlqResult instanceof NullMessage) {
            // Try ack
            handleNullMessage(sourceMessage, sqsListenerRetryableHandler);
        } else {
            log.error("Not deleting message due to dead-letter-queue-forwarding error");
        }
    }

    protected Message<?> /* NOSONAR */ deleteMessage(software.amazon.awssdk.services.sqs.model.Message message) {
        try {
            return sqsAsyncClient.deleteMessage(b -> b.queueUrl(queueUrl).receiptHandle(message.receiptHandle()))
                    .thenApply(r -> NullMessage.INSTANCE)
                    .join();
        } catch (Exception ex) {
            TracingUtils.setErrorOnSpan(FutureUtils.unwrapCompletionException(ex), log, true);
            log.error("Failed to delete message id={} from queue={} - Message might get processed again", message.messageId(), queueUrl, FutureUtils.unwrapCompletionException(ex));
            return new ErrorMessage(FutureUtils.unwrapCompletionException(ex));
        }
    }

    protected Message<?> /* NOSONAR */ forwardDeadLetterQueue(software.amazon.awssdk.services.sqs.model.Message message) {
        if (redrivePolicy == null) {
            log.debug("Cannot forward message id={} from queue={} to dead-letter-queue because no redrive policy is defined", message.messageId(), queueUrl);
            return NullMessage.INSTANCE;
        }
        try {
            SendMessageResponse response = sqsAsyncClient.sendMessage(DeadLetterQueueUtils.buildSendMessageRequest(redrivePolicy.getDeadLetterTargetQueueUrl(), message)).join();
            log.info("Successfully forwarded message id={} from queue={} to dead-letter-queue={} with id={}", message.messageId(), queueUrl, redrivePolicy.getDeadLetterTargetQueueUrl(), response.messageId());
            return NullMessage.INSTANCE;
        } catch (CompletionException | CancellationException ex) {
            TracingUtils.setErrorOnSpan(ex, log, true);
            log.error("Failed to forward message id={} from queue={} to dead-letter-queue={}", message.messageId(), queueUrl, redrivePolicy.getDeadLetterTargetQueueUrl(), FutureUtils.unwrapCompletionException(ex));
            return new ErrorMessage(FutureUtils.unwrapCompletionException(ex));
        }
    }

    private Span buildChildSpan() {
        return GlobalTracer.get()
                .buildSpan(CONVERT_SPAN_OPERATION_NAME)
                .withTag(DDTags.RESOURCE_NAME, CONVERT_MESSAGE_SPAN_RESOURCE_NAME)
                .start();
    }

    private void decorateTripsContextFromHeaders(software.amazon.awssdk.services.sqs.model.Message message) {
        message.messageAttributes().forEach((k, v) -> {
            if ("eventId".equalsIgnoreCase(k)) {
                TripsContextDecorator.getInstance().getEventId().put(v.stringValue());
            } else if ("businessProcessId".equalsIgnoreCase(k)) {
                TripsContextDecorator.getInstance().getBusinessProcessId().put(Optional.ofNullable(v.stringValue()).map(UUID::fromString).orElse(null));
            } else if ("businessProcessName".equalsIgnoreCase(k)) {
                TripsContextDecorator.getInstance().getBusinessProcessName().put(v.stringValue());
            }
        });
    }

    private AcknowledgementCallback<T> wrapAckCallback() {
        return new AcknowledgementCallbackWrapper<>(getAcknowledgementCallback());
    }

    private org.springframework.messaging.Message<T> enrichContext(org.springframework.messaging.Message<T> message, SqsListenerRetryableHandler sqsListenerRetryableHandler) {
        Map<String, Object> newHeaders = HashMap.newHashMap(4);
        newHeaders.put(CONTEXT_SNAPSHOT_HEADER, ContextSnapshotUtils.capture());
        newHeaders.put(SqsHeaders.SQS_QUEUE_ATTRIBUTES_HEADER, getExtendedQueueAttributes());
        newHeaders.put(MessagingHeaders.ACKNOWLEDGMENT_CALLBACK_HEADER, wrapAckCallback());
        newHeaders.put(MESSAGE_HEADER_RETRYABLE_HANDLER, sqsListenerRetryableHandler);
        return IdTsAwareMessageUtils.addHeaders(message, newHeaders);
    }

    private SqsListenerRetryableHandler buildSqsListenerRetryableHandler(software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        int approxReceiveCount = Integer.parseInt(sourceMessage.attributes().get(MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT));
        int retriesLeft;
        boolean manualAckRequired;
        if (redrivePolicy == null) {
            if (extendedQueueAttributes.isFifo()) {
                retriesLeft = Math.max((maxRetriesNoDlq * maxMessagesPerRequest) - approxReceiveCount, 0);
            } else {
                retriesLeft = Math.max(maxRetriesNoDlq - approxReceiveCount, 0);
            }
            // Message must be ack manually because it is never moved to DLQ
            manualAckRequired = true;
        } else if (extendedQueueAttributes.isFifo()) {
            int maxReceiveCount = redrivePolicy.getMaxReceiveCount();
            // Divide available DLQ receive count by maxMessagesPerRequest
            // to make sure the first message is deleted after 1/maxMessagesPerRequest of available retries
            int maxReceiveCountPerMsg = (int) Math.floor((double) maxReceiveCount / maxMessagesPerRequest);
            retriesLeft = Math.max(maxReceiveCountPerMsg - approxReceiveCount, 0);
            // Message must be ack manually after 1/maxMessagesPerRequest of available retries
            manualAckRequired = true;
        } else {
            int maxReceiveCount = redrivePolicy.getMaxReceiveCount();
            retriesLeft = Math.max(maxReceiveCount - approxReceiveCount, 0);
            manualAckRequired = false;
        }
        return new SqsListenerRetryableHandler(manualAckRequired, retriesLeft);
    }

    private void decorateLoggingContext(org.springframework.messaging.Message<?> message) {
        try {
            loggingContextCustomizers.forEach(c -> c.setupContext(message));
        } catch (Exception ex) {
            TracingUtils.setErrorOnSpan(ex, log, false);
            log.error("An unexpected error occurred while decorating logging context - Continue to process message", ex);
        }
    }

    private void logFifoOrderNoLongerGuaranteed(software.amazon.awssdk.services.sqs.model.Message message) {
        log.error(MonitoringMarkerUtils.appendForMonitoringMood(MonitoringMoodData.Mood.BOMB), "The order of FIFO message group={} can no longer be guaranteed", message.attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID));
    }
}
