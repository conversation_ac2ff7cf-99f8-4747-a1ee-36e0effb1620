package com.tui.destilink.framework.aws.sqs.logging.context;

import com.tui.destilink.framework.aws.sqs.util.SqsMessageConversionContextUtils;
import com.tui.destilink.framework.core.logging.context.AbstractContextMapDecorator;
import io.awspring.cloud.sqs.support.converter.SqsMessageConversionContext;
import lombok.Getter;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName;

import java.util.Set;

@SuppressWarnings("squid:S6548")
public class SqsMessageContextDecorator extends AbstractContextMapDecorator<SqsMessageContextDecorator> {

    private static final SqsMessageContextDecorator INSTANCE = new SqsMessageContextDecorator();

    public static final String PREFIX = "aws.sqs.";

    private static final String QUEUE_URL_KEY_SUFFIX = "queueUrl";
    private static final String MESSAGE_ID_KEY_SUFFIX = "messageId";
    private static final String MESSAGE_GROUP_ID_KEY_SUFFIX = "messageGroupId";
    private static final String MESSAGE_DEDUPLICATION_ID_KEY_SUFFIX = "messageDeduplicationId";

    @Getter
    private final Property<String> queueUrl = super.buildStringProperty(QUEUE_URL_KEY_SUFFIX);
    @Getter
    private final Property<String> messageId = super.buildStringProperty(MESSAGE_ID_KEY_SUFFIX);
    @Getter
    private final Property<String> messageGroupId = super.buildStringProperty(MESSAGE_GROUP_ID_KEY_SUFFIX);
    @Getter
    private final Property<String> messageDeduplicationId = super.buildStringProperty(MESSAGE_DEDUPLICATION_ID_KEY_SUFFIX);

    private final Set<String> keys;

    private SqsMessageContextDecorator() {
        super(PREFIX);
        this.keys = Set.of(queueUrl.getKey(), messageId.getKey(), messageGroupId.getKey(), messageDeduplicationId.getKey());
    }

    public static SqsMessageContextDecorator getInstance() {
        return INSTANCE;
    }

    @Override
    protected Set<String> getKeys() {
        return keys;
    }

    public static void decorate(Message message, SqsMessageConversionContext context) {
        getInstance()
                .getQueueUrl().put(SqsMessageConversionContextUtils.extractQueueUrl(context))
                .getMessageId().put(message.messageId())
                .getMessageGroupId().put(message.attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID))
                .getMessageDeduplicationId().put(message.attributes().get(MessageSystemAttributeName.MESSAGE_DEDUPLICATION_ID));
    }
}
