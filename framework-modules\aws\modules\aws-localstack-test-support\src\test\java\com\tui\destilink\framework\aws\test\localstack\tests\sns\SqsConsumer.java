package com.tui.destilink.framework.aws.test.localstack.tests.sns;

import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.listener.Visibility;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import lombok.Data;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.tui.destilink.framework.aws.test.localstack.util.LocalStackPropertiesResolver.MESSAGING_PROPERTY_PREFIX;

@Data
public class SqsConsumer {

    private final List<String> payloads = new ArrayList<>();

    private final List<String> fifoPayloads = new ArrayList<>();

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + "hello-queue.name}")
    public void helloQueueListener(
            Acknowledgement acknowledgement,
            Visibility visibility,
            @Headers Map<String, Object> headers,
            @Payload String payload
    ) {
        payloads.add(payload);
        acknowledgement.acknowledge();
    }

    @SqsListener(queueNames = "${" + MESSAGING_PROPERTY_PREFIX + "hello-queue.fifo.name}")
    public void helloQueueFifoListener(
            Acknowledgement acknowledgement,
            Visibility visibility,
            @Headers Map<String, Object> headers,
            @Payload String payload
    ) {
        fifoPayloads.add(payload);
        acknowledgement.acknowledge();
    }

}
