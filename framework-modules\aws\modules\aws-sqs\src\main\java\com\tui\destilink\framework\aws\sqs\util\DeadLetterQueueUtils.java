package com.tui.destilink.framework.aws.sqs.util;

import com.tui.destilink.framework.aws.sqs.components.message_source.ExtendedQueueAttributes;
import com.tui.destilink.framework.core.tracing.TracingUtils;
import com.tui.destilink.framework.core.util.FutureUtils;
import io.awspring.cloud.sqs.listener.SqsHeaders;
import io.awspring.cloud.sqs.support.converter.MessageAttributeDataTypes;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.*;

import java.util.EnumMap;
import java.util.Map;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletionException;

@Slf4j
@UtilityClass
public class DeadLetterQueueUtils {

    public static boolean forwardMessage(SqsAsyncClient sqsAsyncClient, Message<?> message) {
        try {
            ExtendedQueueAttributes queueAttributes = message.getHeaders().get(SqsHeaders.SQS_QUEUE_ATTRIBUTES_HEADER, ExtendedQueueAttributes.class);
            software.amazon.awssdk.services.sqs.model.Message sourceMessage = message.getHeaders().get(SqsHeaders.SQS_SOURCE_DATA_HEADER, software.amazon.awssdk.services.sqs.model.Message.class);
            String sourceQueueUrl = message.getHeaders().get(SqsHeaders.SQS_QUEUE_URL_HEADER, String.class);
            Assert.notNull(queueAttributes, "queueAttributes must not be null");
            Assert.notNull(sourceMessage, "sourceMessage must not be null");
            Assert.notNull(sourceQueueUrl, "sourceQueueUrl must not be null");
            return forwardMessage(sqsAsyncClient, queueAttributes.getRedrivePolicy(), sourceMessage, sourceQueueUrl);
        } catch (Exception ex) {
            log.error("An unexpected error occurred while sending message id={} to the dead-letter-queue", message.getHeaders().getId(), ex);
            return false;
        }
    }

    public static boolean forwardMessage(SqsAsyncClient sqsAsyncClient, ExtendedQueueAttributes.RedrivePolicy redrivePolicy, software.amazon.awssdk.services.sqs.model.Message sourceMessage, String sourceQueueUrl) {
        if (redrivePolicy == null) {
            log.warn("Cannot forward message id={} from queue={} to DLQ because no redrive policy is defined", sourceMessage.messageId(), sourceQueueUrl);
            return true;
        }
        try {
            SendMessageResponse response = sqsAsyncClient.sendMessage(DeadLetterQueueUtils.buildSendMessageRequest(redrivePolicy.getDeadLetterTargetQueueUrl(), sourceMessage)).join();
            log.info("Successfully forwarded message id={} from queue={} to dead-letter-queue={} with id={}", sourceMessage.messageId(), sourceQueueUrl, redrivePolicy.getDeadLetterTargetQueueUrl(), response.messageId());
            return true;
        } catch (CompletionException | CancellationException ex) {
            TracingUtils.setErrorOnSpan(ex, log, true);
            log.error("Failed to forward message id={} from queue={} to dead-letter-queue={}", sourceMessage.messageId(), sourceQueueUrl, redrivePolicy.getDeadLetterTargetQueueUrl(), FutureUtils.unwrapCompletionException(ex));
            return false;
        }
    }

    public static SendMessageRequest buildSendMessageRequest(String deadLetterQueueUrl, software.amazon.awssdk.services.sqs.model.Message sourceMessage) {
        SendMessageRequest.Builder builder = SendMessageRequest.builder()
                .queueUrl(deadLetterQueueUrl)
                .messageBody(sourceMessage.body())
                .messageSystemAttributes(buildSystemAttributes(sourceMessage))
                .messageAttributes(sourceMessage.messageAttributes());

        if (sourceMessage.attributes().containsKey(MessageSystemAttributeName.MESSAGE_GROUP_ID)) {
            builder.messageGroupId(sourceMessage.attributes().get(MessageSystemAttributeName.MESSAGE_GROUP_ID));
        }
        if (sourceMessage.attributes().containsKey(MessageSystemAttributeName.MESSAGE_DEDUPLICATION_ID)) {
            builder.messageDeduplicationId(sourceMessage.attributes().get(MessageSystemAttributeName.MESSAGE_DEDUPLICATION_ID));
        }

        return builder.build();
    }

    public static Map<MessageSystemAttributeNameForSends, MessageSystemAttributeValue> buildSystemAttributes(software.amazon.awssdk.services.sqs.model.Message message) {
        Map<MessageSystemAttributeNameForSends, MessageSystemAttributeValue> systemAttributes = new EnumMap<>(MessageSystemAttributeNameForSends.class);
        String awsTraceHeader = message.attributes().get(MessageSystemAttributeName.AWS_TRACE_HEADER);
        if (StringUtils.hasLength(awsTraceHeader)) {
            systemAttributes.put(MessageSystemAttributeNameForSends.AWS_TRACE_HEADER, MessageSystemAttributeValue.builder()
                    .dataType(MessageAttributeDataTypes.STRING).stringValue(awsTraceHeader).build());
        }
        return systemAttributes;
    }
}
