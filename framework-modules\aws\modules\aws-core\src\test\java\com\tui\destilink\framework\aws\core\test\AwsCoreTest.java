package com.tui.destilink.framework.aws.core.test;

import com.tui.destilink.framework.aws.core.identity.AwsCallerIdentity;
import com.tui.destilink.framework.aws.core.identity.annotation.AwsAccountId;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.regions.providers.AwsRegionProvider;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestApplication.class, AwsCoreTest.Config.class})
@ActiveProfiles({"it"})
class AwsCoreTest {

    @TestConfiguration
    static class Config {
        @Bean
        public AwsCallerIdentity awsCallerIdentity() {
            return new AwsCallerIdentity("account-id", "user-id", "arn");
        }
    }

    @Autowired
    private AwsRegionProvider awsRegionProvider;

    @AwsAccountId
    private String accountId;

    @Autowired(required = false)
    private AwsCallerIdentity awsCallerIdentity;

    @Test
    void testRegion() {
        // Region must be eu-central-1 by default
        assertThat(awsRegionProvider).isNotNull();
        assertThat(awsRegionProvider.getRegion()).isEqualTo(Region.EU_CENTRAL_1);
    }

    @Test
    void testCallerIdentity() {
        assertThat(awsCallerIdentity).isNotNull();
        assertThat(awsCallerIdentity.getAccountId()).isEqualTo("account-id");
        assertThat(accountId).isEqualTo("account-id");
    }

}
