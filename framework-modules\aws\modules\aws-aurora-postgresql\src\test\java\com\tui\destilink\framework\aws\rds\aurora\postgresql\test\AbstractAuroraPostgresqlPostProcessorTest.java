package com.tui.destilink.framework.aws.rds.aurora.postgresql.test;

import com.tui.destilink.framework.aws.rds.aurora.postgresql.config.AuroraPostgresqlConfig;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@ActiveProfiles({"tests"})
@EnableConfigurationProperties(AbstractAuroraPostgresqlPostProcessorTest.ConfigProps.class)
@EnableAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
abstract class AbstractAuroraPostgresqlPostProcessorTest {

    @Data
    @ConfigurationProperties("data-sources")
    static class ConfigProps {
        private Map<String, Object> test1;
        private Map<String, Object> test2;
        private Map<String, Object> test3;
        private Map<String, Object> test4Ro;
        private Map<String, Object> test5Ro;
        private Map<String, Object> test6Ro;
        private Map<String, Object> proxyRo;
    }

    @Autowired
    protected ConfigProps props;

    @Autowired
    protected AuroraPostgresqlConfig auroraPostgresqlConfig;

    protected String testGetSomeUserUsername() {
        return "someuser";
    }

    protected String test1WrapperPluginsPrefix() {
        return "";
    }

    @SuppressWarnings("unchecked")
    @Test
    void testInjectDriverAndDefaults() {
        assertThat(get(props.test1, "url")).isEqualTo("jdbc:aws-wrapper:postgresql://some.endpoint:5432/postgres");
        assertThat(get(props.test1, "username")).isEqualTo(testGetSomeUserUsername());
        assertThat(get(props.test1, "password")).isEqualTo("somepassword");
        assertThat(get(props.test1, "driver-class-name")).isEqualTo("software.amazon.jdbc.Driver");
        assertThat(get(props.test1, "hikari.exception-override-class-name")).isEqualTo("software.amazon.jdbc.util.HikariCPSQLException");
        assertThat((Map<String, Object>) get(props.test1, "hikari.data-source-properties")).hasSize(11);
        assertThat(get(props.test1, "hikari.data-source-properties.wrapperPlugins")).isEqualTo(test1WrapperPluginsPrefix() + "driverMetaData,auroraConnectionTracker,failover2,efm2");
        assertThat(get(props.test1, "hikari.data-source-properties.wrapperDriverName")).isEqualTo("PostgreSQL JDBC Driver");
        assertThat(get(props.test1, "hikari.data-source-properties.monitoring-connectTimeout")).isEqualTo(10);
        assertThat(get(props.test1, "hikari.data-source-properties.monitoring-socketTimeout")).isEqualTo(10);
        assertThat(get(props.test1, "hikari.data-source-properties.failoverClusterTopologyRefreshRateMs")).isEqualTo(2000);
        assertThat(get(props.test1, "hikari.data-source-properties.failoverWriterReconnectIntervalMs")).isEqualTo(2000);
        assertThat(get(props.test1, "hikari.data-source-properties.reWriteBatchedInserts")).isEqualTo(true);
        assertThat(get(props.test1, "hikari.data-source-properties.ssl")).isEqualTo(true);
        assertThat(get(props.test1, "hikari.data-source-properties.sslmode")).isEqualTo("require");
        assertThat(get(props.test1, "hikari.data-source-properties.tcpKeepAlive")).isEqualTo(true);
        assertThat(get(props.test1, "hikari.data-source-properties.ApplicationName")).isEqualTo("hey-sexy-service");
    }

    @SuppressWarnings("unchecked")
    @Test
    void testInjectUrlAndOverrideDefaults() {
        assertThat(get(props.test2, "url")).isEqualTo("jdbc:aws-wrapper:postgresql://some.endpoint:5432/postgres");
        assertThat(get(props.test2, "username")).isEqualTo(testGetSomeUserUsername());
        assertThat(get(props.test2, "password")).isEqualTo("somepassword");
        assertThat(get(props.test2, "driver-class-name")).isEqualTo("software.amazon.jdbc.Driver");
        assertThat(get(props.test2, "hikari.exception-override-class-name")).isEqualTo("some.exception.to.not.override");
        assertThat((Map<String, Object>) get(props.test2, "hikari.data-source-properties")).hasSize(12);
        assertThat(get(props.test2, "hikari.data-source-properties.wrapperDialect")).isEqualTo("aurora-pg");
        assertThat(get(props.test2, "hikari.data-source-properties.wrapperPlugins")).isEqualTo("");
        assertThat(get(props.test2, "hikari.data-source-properties.wrapperDriverName")).isEqualTo("other driver name");
        assertThat(get(props.test2, "hikari.data-source-properties.monitoring-connectTimeout")).isEqualTo(20);
        assertThat(get(props.test2, "hikari.data-source-properties.monitoring-socketTimeout")).isEqualTo(20);
        assertThat(get(props.test1, "hikari.data-source-properties.failoverClusterTopologyRefreshRateMs")).isEqualTo(2000);
        assertThat(get(props.test1, "hikari.data-source-properties.failoverWriterReconnectIntervalMs")).isEqualTo(2000);
    }

    @Test
    void testDoNothing() {
        assertThat(props.test3).hasSize(3);
        assertThat(get(props.test3, "url")).isEqualTo("*********************************************");
        assertThat(get(props.test3, "username")).isEqualTo("someuser");
        assertThat(get(props.test3, "password")).isEqualTo("somepassword");
    }

    @SuppressWarnings("unchecked")
    @Test
    void testRoMaxLifetimeExists() {
        assertThat(get(props.test4Ro, "url")).isEqualTo("jdbc:aws-wrapper:postgresql://xxxxxxxx.cluster-ro-xxxxx.eu-central-1.rds.amazonaws.com:5432/postgres");
        assertThat((Map<String, Object>) get(props.test4Ro, "hikari"))
                .containsEntry("max-lifetime", 1000)
                .doesNotContainKey("maxLifetime");
    }

    @SuppressWarnings("unchecked")
    @Test
    void testRoMaxLifetimeExistsCamelCase() {
        assertThat(get(props.test5Ro, "url")).isEqualTo("jdbc:aws-wrapper:postgresql://xxxxxxxx.cluster-ro-xxxxx.eu-central-1.rds.amazonaws.com:5432/postgres?SomeOtherProperty=hello-world");
        assertThat((Map<String, Object>) get(props.test5Ro, "hikari"))
                .containsEntry("maxLifetime", 2000)
                .doesNotContainKey("max-lifetime");
    }

    @SuppressWarnings("unchecked")
    @Test
    void testRoMaxLifetimeNotExists() {
        assertThat(get(props.test6Ro, "url")).isEqualTo("jdbc:aws-wrapper:postgresql://xxxxxxxx.cluster-ro-xxxxx.eu-central-1.rds.amazonaws.com:5432/postgres");
        assertThat((Map<String, Object>) get(props.test6Ro, "hikari"))
                .containsEntry("max-lifetime", 300000L)
                .doesNotContainKey("maxLifetime");
    }

    @SuppressWarnings("unchecked")
    @Test
    void testRoProxyMaxLifetimeNotExists() {
        assertThat(get(props.proxyRo, "url")).isEqualTo("jdbc:aws-wrapper:postgresql://lllll-read-only.endpoint.proxy-clqugzc7wayt.eu-central-1.rds.amazonaws.com:5432/postgres");
        assertThat((Map<String, Object>) get(props.proxyRo, "hikari"))
                .containsEntry("max-lifetime", 300000L)
                .doesNotContainKey("maxLifetime");
    }

    private Object get(Map<String, Object> properties, String propertyKey) {
        LinkedList<String> list = new LinkedList<>(Arrays.asList(propertyKey.split("\\.")));
        return get(properties, list);
    }

    @SuppressWarnings("unchecked")
    private Object get(Map<String, Object> properties, List<String> propertyKey) {
        String key = propertyKey.removeFirst();
        if (propertyKey.isEmpty()) {
            return properties.get(key);
        }
        Map<String, Object> subMap = (Map<String, Object>) properties.get(key);
        return get(subMap, propertyKey);
    }

}
